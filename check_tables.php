<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';
global $wpdb;

$tables = $wpdb->get_results('SHOW TABLES');
echo 'All tables:' . PHP_EOL;
foreach ($tables as $table) {
    $table_array = (array) $table;
    echo '  - ' . array_values($table_array)[0] . PHP_EOL;
}

// Check specifically for custom_sections table
$table_name = $wpdb->prefix . 'custom_sections';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
echo PHP_EOL . 'Custom sections table (' . $table_name . ') exists: ' . ($table_exists ? 'YES' : 'NO') . PHP_EOL;

// Try to create table with simpler structure
if (!$table_exists) {
    echo 'Creating table...' . PHP_EOL;
    
    $sql = "CREATE TABLE $table_name (
        id int(11) NOT NULL AUTO_INCREMENT,
        name varchar(255) NOT NULL,
        shortcode varchar(100) NOT NULL,
        description text,
        html_content longtext NOT NULL,
        css_content longtext,
        js_content longtext,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
    
    $result = $wpdb->query($sql);
    
    if ($result === false) {
        echo 'Table creation failed: ' . $wpdb->last_error . PHP_EOL;
    } else {
        echo 'Table created successfully!' . PHP_EOL;
    }
    
    // Check again
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
    echo 'Table exists after creation: ' . ($table_exists ? 'YES' : 'NO') . PHP_EOL;
}
