@extends('layouts.app')

@section('title', 'Create Campaign')
@section('page-title', 'Create New Campaign')

@section('content')
<div class="max-w-4xl mx-auto">
    <form action="{{ route('campaigns.store') }}" method="POST" class="space-y-8">
        @csrf
        
        <!-- Basic Information -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">Basic Information</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-300">Campaign Name</label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                               class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                        @error('name')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="sm:col-span-2">
                        <label for="keywords" class="block text-sm font-medium text-gray-300">Keywords</label>
                        <p class="text-sm text-gray-400 mb-2">Add one or more keywords for your campaign. Each keyword can have its own FAQ questions.</p>

                        <div id="keywords-container" class="space-y-4">
                            <div class="keyword-item border border-gray-600 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-sm font-medium text-white">Keyword #1</h4>
                                    <button type="button" onclick="removeKeywordItem(this)" class="text-red-400 hover:text-red-300 hidden">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>

                                <div class="mb-3">
                                    <input type="text" name="keywords[]" placeholder="Enter keyword..." required
                                           class="w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                                </div>

                                <div class="faq-section">
                                    <label class="block text-sm font-medium text-gray-300 mb-2">FAQ Questions for this keyword</label>
                                    <div class="faq-questions space-y-2">
                                        <div class="faq-question-item flex items-center space-x-2">
                                            <input type="text" name="keyword_faqs[0][]" placeholder="Enter a question..."
                                                   class="flex-1 bg-gray-600 border border-gray-500 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                                            <button type="button" onclick="removeFaqQuestion(this)" class="text-red-400 hover:text-red-300">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <button type="button" onclick="addFaqQuestion(this)" class="mt-2 text-sm text-cyan-400 hover:text-cyan-300">
                                        + Add FAQ Question
                                    </button>
                                </div>
                            </div>
                        </div>

                        <button type="button" onclick="addKeywordItem()" class="mt-3 inline-flex items-center px-3 py-2 border border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Another Keyword
                        </button>

                        @error('keywords')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="sm:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-300">Description</label>
                        <textarea name="description" id="description" rows="3"
                                  class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Information -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">Business Information</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                        <label for="business_name" class="block text-sm font-medium text-gray-300">Business Name</label>
                        <input type="text" name="business_name" id="business_name" value="{{ old('business_name') }}" required
                               class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                        @error('business_name')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="contact_phone" class="block text-sm font-medium text-gray-300">Contact Phone</label>
                        <input type="text" name="contact_phone" id="contact_phone" value="{{ old('contact_phone') }}"
                               class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                        @error('contact_phone')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="contact_email" class="block text-sm font-medium text-gray-300">Contact Email</label>
                        <input type="email" name="contact_email" id="contact_email" value="{{ old('contact_email') }}"
                               class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                        @error('contact_email')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="address" class="block text-sm font-medium text-gray-300">Address</label>
                        <input type="text" name="address" id="address" value="{{ old('address') }}"
                               class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                        @error('address')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="sm:col-span-2">
                        <label for="business_description" class="block text-sm font-medium text-gray-300">Business Description</label>
                        <textarea name="business_description" id="business_description" rows="3"
                                  class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">{{ old('business_description') }}</textarea>
                        @error('business_description')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="sm:col-span-2">
                        <label for="wordpress_connection_id" class="block text-sm font-medium text-gray-300">WordPress Connection</label>
                        <p class="text-sm text-gray-400 mb-2">Select which WordPress site to use for exporting pages from this campaign.</p>
                        <select name="wordpress_connection_id" id="wordpress_connection_id"
                                class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                            <option value="">Select WordPress Connection (Optional)</option>
                            @foreach($wordpressConnections as $connection)
                                <option value="{{ $connection->id }}" {{ old('wordpress_connection_id') == $connection->id ? 'selected' : '' }}
                                        @if($connection->is_default) selected @endif>
                                    {{ $connection->name }} - {{ $connection->url }}
                                    @if($connection->is_default) (Default) @endif
                                </option>
                            @endforeach
                        </select>
                        @error('wordpress_connection_id')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                        @if($wordpressConnections->isEmpty())
                            <p class="mt-1 text-sm text-yellow-400">
                                No WordPress connections available.
                                <a href="{{ route('wordpress.connections.create') }}" class="text-cyan-400 hover:text-cyan-300">Create one here</a>.
                            </p>
                        @endif
                    </div>

                    <div class="sm:col-span-2">
                        <label for="template_id" class="block text-sm font-medium text-gray-300">Landing Page Template</label>
                        <p class="text-sm text-gray-400 mb-2">Select a template for generating landing pages from this campaign.</p>
                        <select name="template_id" id="template_id"
                                class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                            <option value="">Select Template (Optional)</option>
                            @foreach($templates as $template)
                                <option value="{{ $template->id }}" {{ old('template_id') == $template->id ? 'selected' : '' }}>
                                    {{ $template->name }}
                                    @if($template->is_processed)
                                        ({{ count($template->placeholders ?? []) }} placeholders)
                                    @else
                                        (Not processed yet)
                                    @endif
                                </option>
                            @endforeach
                        </select>
                        @error('template_id')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                        @if($templates->isEmpty())
                            <p class="mt-1 text-sm text-yellow-400">
                                No templates available.
                                <a href="{{ route('templates.create') }}" class="text-cyan-400 hover:text-cyan-300">Create one here</a>.
                            </p>
                        @endif
                    </div>
                </div>
            </div>
        </div>



        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="{{ route('campaigns.index') }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                Cancel
            </a>
            <button type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500">
                Create Campaign
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
let keywordCount = 1;

function addKeywordItem() {
    const container = document.getElementById('keywords-container');
    const newKeywordIndex = keywordCount;
    keywordCount++;

    const newItem = document.createElement('div');
    newItem.className = 'keyword-item border border-gray-600 rounded-lg p-4';
    newItem.innerHTML = `
        <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-medium text-white">Keyword #${keywordCount}</h4>
            <button type="button" onclick="removeKeywordItem(this)" class="text-red-400 hover:text-red-300">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>

        <div class="mb-3">
            <input type="text" name="keywords[]" placeholder="Enter keyword..." required
                   class="w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
        </div>

        <div class="faq-section">
            <label class="block text-sm font-medium text-gray-300 mb-2">FAQ Questions for this keyword</label>
            <div class="faq-questions space-y-2">
                <div class="faq-question-item flex items-center space-x-2">
                    <input type="text" name="keyword_faqs[${newKeywordIndex}][]" placeholder="Enter a question..."
                           class="flex-1 bg-gray-600 border border-gray-500 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                    <button type="button" onclick="removeFaqQuestion(this)" class="text-red-400 hover:text-red-300">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <button type="button" onclick="addFaqQuestion(this)" class="mt-2 text-sm text-cyan-400 hover:text-cyan-300">
                + Add FAQ Question
            </button>
        </div>
    `;
    container.appendChild(newItem);
    updateRemoveButtons();
}

function removeKeywordItem(button) {
    const container = document.getElementById('keywords-container');
    if (container.children.length > 1) {
        button.closest('.keyword-item').remove();
        updateKeywordNumbers();
        updateRemoveButtons();
    }
}

function addFaqQuestion(button) {
    const faqSection = button.closest('.faq-section');
    const faqQuestions = faqSection.querySelector('.faq-questions');
    const keywordItem = button.closest('.keyword-item');
    const keywordIndex = Array.from(keywordItem.parentNode.children).indexOf(keywordItem);

    const newQuestion = document.createElement('div');
    newQuestion.className = 'faq-question-item flex items-center space-x-2';
    newQuestion.innerHTML = `
        <input type="text" name="keyword_faqs[${keywordIndex}][]" placeholder="Enter a question..."
               class="flex-1 bg-gray-600 border border-gray-500 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
        <button type="button" onclick="removeFaqQuestion(this)" class="text-red-400 hover:text-red-300">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;
    faqQuestions.appendChild(newQuestion);
}

function removeFaqQuestion(button) {
    const faqQuestions = button.closest('.faq-questions');
    if (faqQuestions.children.length > 1) {
        button.closest('.faq-question-item').remove();
    }
}

function updateKeywordNumbers() {
    const keywordItems = document.querySelectorAll('.keyword-item');
    keywordItems.forEach((item, index) => {
        const title = item.querySelector('h4');
        title.textContent = `Keyword #${index + 1}`;

        // Update FAQ input names
        const faqInputs = item.querySelectorAll('input[name^="keyword_faqs"]');
        faqInputs.forEach(input => {
            input.name = `keyword_faqs[${index}][]`;
        });
    });
}

function updateRemoveButtons() {
    const keywordItems = document.querySelectorAll('.keyword-item');
    keywordItems.forEach((item, index) => {
        const removeButton = item.querySelector('button[onclick="removeKeywordItem(this)"]');
        if (keywordItems.length === 1) {
            removeButton.classList.add('hidden');
        } else {
            removeButton.classList.remove('hidden');
        }
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateRemoveButtons();
});
</script>
@endpush
@endsection
