<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Template extends Model
{
    protected $fillable = [
        'name',
        'description',
        'sections',
        'styles',
        'scripts',
        'fonts',
        'master_prompt',
        'placeholders',
        'is_processed',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'sections' => 'array',
        'placeholders' => 'array',
        'is_processed' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the generated pages for the template.
     */
    public function generatedPages(): HasMany
    {
        return $this->hasMany(GeneratedPage::class);
    }

    /**
     * Generate complete HTML from sections, styles, scripts, and fonts.
     */
    public function generateCompleteHtml(): string
    {
        $html = "<!DOCTYPE html>\n<html lang=\"id\">\n<head>\n";
        $html .= "<meta charset=\"UTF-8\">\n";
        $html .= "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n";
        $html .= "<title>{{Yoast_title}}</title>\n";
        $html .= "<meta name=\"description\" content=\"{{Yoast_description}}\">\n";
        
        // Add fonts
        if ($this->fonts) {
            $html .= $this->fonts . "\n";
        }
        
        // Add scripts (CSS frameworks, JS libraries)
        if ($this->scripts) {
            $html .= $this->scripts . "\n";
        }
        
        // Add custom styles
        if ($this->styles) {
            $html .= "<style>\n" . $this->styles . "\n</style>\n";
        }
        
        $html .= "</head>\n<body>\n";
        
        // Add sections content
        if ($this->sections) {
            $sortedSections = collect($this->sections)->sortBy('order');
            foreach ($sortedSections as $section) {
                $html .= $section['html_content'] . "\n";
            }
        }
        
        $html .= "</body>\n</html>";
        
        return $html;
    }

    /**
     * Process template to generate placeholders from P and H1-H6 tags.
     */
    public function processPlaceholders(): array
    {
        $placeholders = [];
        
        if (!$this->sections) {
            return $placeholders;
        }
        
        foreach ($this->sections as $index => $section) {
            $sectionName = $this->sanitizeSectionName($section['name']);
            $htmlContent = $section['html_content'];
            
            // Extract text from P tags
            preg_match_all('/<p[^>]*>(.*?)<\/p>/is', $htmlContent, $pMatches);
            foreach ($pMatches[1] as $pIndex => $pContent) {
                $cleanContent = strip_tags($pContent);
                if (!empty(trim($cleanContent))) {
                    $placeholderName = $sectionName . '_paragraph_' . ($pIndex + 1);
                    $placeholders[$placeholderName] = $cleanContent;
                }
            }
            
            // Extract text from H1-H6 tags
            for ($h = 1; $h <= 6; $h++) {
                preg_match_all("/<h{$h}[^>]*>(.*?)<\/h{$h}>/is", $htmlContent, $hMatches);
                foreach ($hMatches[1] as $hIndex => $hContent) {
                    $cleanContent = strip_tags($hContent);
                    if (!empty(trim($cleanContent))) {
                        $placeholderName = $sectionName . '_h' . $h . '_' . ($hIndex + 1);
                        $placeholders[$placeholderName] = $cleanContent;
                    }
                }
            }
        }
        
        return $placeholders;
    }

    /**
     * Generate master prompt based on placeholders.
     */
    public function generateMasterPrompt(): string
    {
        $placeholders = $this->processPlaceholders();

        // Create clean JSON structure
        $jsonStructure = [
            'seo_fields' => [
                'yoast_title' => '',
                'yoast_description' => '',
                'yoast_focus_keyword' => '',
                'schema_business_name' => '',
                'schema_business_type' => '',
                'schema_description' => '',
                'schema_telephone' => '',
                'schema_address' => '',
                'schema_email' => ''
            ],
            'content_fields' => []
        ];

        // Add all placeholders to content_fields
        foreach ($placeholders as $placeholder => $description) {
            $jsonStructure['content_fields'][$placeholder] = '';
        }

        // Add contact fields
        $jsonStructure['content_fields']['Contact'] = '';
        $jsonStructure['content_fields']['Address'] = '';
        $jsonStructure['content_fields']['Email'] = '';

        return json_encode($jsonStructure, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Replace placeholders in HTML with actual content.
     */
    public function replacePlaceholders(array $data): string
    {
        $html = $this->generateCompleteHtml();

        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $html = str_replace('{{' . $key . '}}', $value, $html);
            }
        }

        return $html;
    }

    /**
     * Check if template has head/main separation.
     * Return true to use the new head/main export method.
     */
    public function hasHeadMainSeparation(): bool
    {
        return true;
    }

    /**
     * Get head content for head/main separation.
     * Extract head content from complete HTML.
     */
    public function getHeadContent(): string
    {
        $html = $this->generateCompleteHtml();

        // Extract head content
        if (preg_match('/<head>(.*?)<\/head>/s', $html, $matches)) {
            return $matches[1];
        }

        return '';
    }

    /**
     * Get main content for head/main separation.
     * Extract body content from complete HTML.
     */
    public function getMainContent(): string
    {
        $html = $this->generateCompleteHtml();

        // Extract body content
        if (preg_match('/<body>(.*?)<\/body>/s', $html, $matches)) {
            return $matches[1];
        }

        return '';
    }

    /**
     * Magic getter for head_content property.
     */
    public function getHeadContentAttribute(): string
    {
        return $this->getHeadContent();
    }

    /**
     * Magic getter for main_content property.
     */
    public function getMainContentAttribute(): string
    {
        return $this->getMainContent();
    }

    /**
     * Get section settings for WordPress integration.
     * For now, return empty array as we don't have static sections.
     */
    public function getSectionSettings(): array
    {
        // For legacy templates, we don't have static sections
        // Return empty array to avoid errors
        return [];
    }

    /**
     * Group placeholders by section for better organization.
     */
    private function groupPlaceholdersBySection(array $placeholders): array
    {
        $grouped = [];

        foreach ($placeholders as $placeholder => $originalContent) {
            // Determine section based on placeholder prefix
            $sectionName = $this->determineSectionFromPlaceholder($placeholder);

            // Generate description based on placeholder name and original content
            $description = $this->generatePlaceholderDescription($placeholder, $originalContent);

            $grouped[$sectionName][$placeholder] = $description;
        }

        return $grouped;
    }

    /**
     * Determine section name from placeholder.
     */
    private function determineSectionFromPlaceholder(string $placeholder): string
    {
        $placeholder = strtolower($placeholder);

        if (str_contains($placeholder, 'hero')) return 'HERO SECTION';
        if (str_contains($placeholder, 'benefit')) return 'BENEFITS SECTION';
        if (str_contains($placeholder, 'social_proof') || str_contains($placeholder, 'client_count') || str_contains($placeholder, 'years_experience') || str_contains($placeholder, 'success_rate')) return 'SOCIAL PROOF SECTION';
        if (str_contains($placeholder, 'intro') || str_contains($placeholder, 'problem') || str_contains($placeholder, 'solution')) return 'INTRO EDUCATIONAL SECTION';
        if (str_contains($placeholder, 'feature')) return 'FEATURES SECTION';
        if (str_contains($placeholder, 'process') || str_contains($placeholder, 'step')) return 'WORK PROCESS SECTION';
        if (str_contains($placeholder, 'comparison') || str_contains($placeholder, 'advantage') || str_contains($placeholder, 'competitor')) return 'SERVICE COMPARISON SECTION';
        if (str_contains($placeholder, 'guarantee')) return 'GUARANTEE SECTION';
        if (str_contains($placeholder, 'other_service')) return 'OTHER SERVICES SECTION';
        if (str_contains($placeholder, 'seo_content')) return 'SEO CONTENT SECTION';
        if (str_contains($placeholder, 'faq')) return 'FAQ SECTION';
        if (str_contains($placeholder, 'cta')) return 'CONTACT CTA SECTION';

        return 'GENERAL SECTION';
    }

    /**
     * Generate description for placeholder.
     */
    private function generatePlaceholderDescription(string $placeholder, string $originalContent): string
    {
        $placeholder = strtolower($placeholder);

        // Generate contextual descriptions
        if (str_contains($placeholder, 'headline')) return 'Main headline (compelling, keyword-focused, benefit-driven)';
        if (str_contains($placeholder, 'description')) return 'Detailed description (2-3 sentences, value proposition)';
        if (str_contains($placeholder, 'title')) return 'Section title';
        if (str_contains($placeholder, 'question')) return 'FAQ question';
        if (str_contains($placeholder, 'answer')) return 'FAQ answer (detailed, helpful)';
        if (str_contains($placeholder, 'benefit') && str_contains($placeholder, 'title')) return 'Benefit title';
        if (str_contains($placeholder, 'benefit') && str_contains($placeholder, 'description')) return 'Benefit description';
        if (str_contains($placeholder, 'feature') && str_contains($placeholder, 'title')) return 'Feature title';
        if (str_contains($placeholder, 'feature') && str_contains($placeholder, 'description')) return 'Feature description';
        if (str_contains($placeholder, 'step') && str_contains($placeholder, 'title')) return 'Process step title';
        if (str_contains($placeholder, 'step') && str_contains($placeholder, 'description')) return 'Process step description';
        if (str_contains($placeholder, 'cta') && str_contains($placeholder, 'button')) return 'Call-to-action button text';
        if (str_contains($placeholder, 'trust_badge')) return 'Trust indicator text (e.g., "Dipercaya oleh 500+ Pelanggan")';
        if (str_contains($placeholder, 'client_count')) return 'Number of satisfied clients (e.g., "500+")';
        if (str_contains($placeholder, 'years_experience')) return 'Years of experience (e.g., "10+")';
        if (str_contains($placeholder, 'success_rate')) return 'Success rate percentage (e.g., "98%")';

        // Default description based on original content
        return "Content for {$placeholder}" . ($originalContent ? " (replacing: {$originalContent})" : '');
    }

    /**
     * Sanitize section name for placeholder generation.
     */
    private function sanitizeSectionName(string $name): string
    {
        // Convert to lowercase, replace spaces and special chars with underscore
        $sanitized = strtolower($name);
        $sanitized = preg_replace('/[^a-z0-9]+/', '_', $sanitized);
        $sanitized = trim($sanitized, '_');

        return $sanitized ?: 'section';
    }
}
