@extends('layouts.app')

@section('title', 'Preview - ' . $template->name)
@section('page-title', 'Preview - ' . $template->name)

@section('content')
<div class="min-h-screen bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white">Preview Template</h1>
                    <p class="text-gray-400 mt-1">{{ $template->name }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    <button onclick="openPreviewWindow()" 
                            class="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        Buka di Tab Baru
                    </button>
                    
                    <a href="{{ route('templates.show', $template) }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Preview Info -->
        <div class="bg-yellow-800 border border-yellow-700 rounded-lg p-4 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-100">Preview Information</h3>
                    <div class="mt-2 text-sm text-yellow-200">
                        <p>Preview ini menampilkan template dengan data sample. Konten aktual akan diganti saat generate pages dari data JSON.</p>
                        @if($template->is_processed)
                            <p class="mt-1">Template sudah diproses dengan {{ count($template->placeholders ?? []) }} placeholder.</p>
                        @else
                            <p class="mt-1">Template belum diproses. Klik "Proses Template" untuk menghasilkan placeholder otomatis.</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Frame -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-white">Template Preview</h3>
                    <div class="flex items-center space-x-2">
                        <button onclick="toggleResponsive('desktop')" 
                                class="responsive-btn active px-3 py-1 text-sm rounded bg-gray-700 text-white" data-size="desktop">
                            Desktop
                        </button>
                        <button onclick="toggleResponsive('tablet')" 
                                class="responsive-btn px-3 py-1 text-sm rounded bg-gray-600 text-gray-300" data-size="tablet">
                            Tablet
                        </button>
                        <button onclick="toggleResponsive('mobile')" 
                                class="responsive-btn px-3 py-1 text-sm rounded bg-gray-600 text-gray-300" data-size="mobile">
                            Mobile
                        </button>
                    </div>
                </div>
                
                <div class="border border-gray-600 rounded-lg overflow-hidden">
                    <div id="preview-container" class="transition-all duration-300">
                        <iframe id="preview-frame" 
                                srcdoc="{{ htmlspecialchars($previewHtml) }}" 
                                class="w-full border-0"
                                style="height: 800px;">
                        </iframe>
                    </div>
                </div>
            </div>
        </div>

        <!-- Template Details -->
        <div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Sections Info -->
            @if($template->sections)
                <div class="bg-gray-800 rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Sections ({{ count($template->sections) }})</h3>
                    <div class="space-y-3">
                        @foreach(collect($template->sections)->sortBy('order') as $section)
                            <div class="flex items-center justify-between p-3 bg-gray-700 rounded">
                                <span class="text-white">{{ $section['name'] }}</span>
                                <span class="text-sm text-gray-400">Order: {{ $section['order'] }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Placeholders Info -->
            @if($template->is_processed && $template->placeholders)
                <div class="bg-gray-800 rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Placeholders ({{ count($template->placeholders) }})</h3>
                    <div class="space-y-2 max-h-64 overflow-y-auto">
                        @foreach($template->placeholders as $placeholder => $originalContent)
                            <div class="p-2 bg-gray-700 rounded">
                                <code class="text-sm text-cyan-400">{{ $placeholder }}</code>
                                <p class="text-xs text-gray-300 mt-1">{{ Str::limit($originalContent, 50) }}</p>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function openPreviewWindow() {
    const previewHtml = @json($previewHtml);
    const newWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
    newWindow.document.write(previewHtml);
    newWindow.document.close();
}

function toggleResponsive(size) {
    const container = document.getElementById('preview-container');
    const frame = document.getElementById('preview-frame');
    const buttons = document.querySelectorAll('.responsive-btn');
    
    // Update button states
    buttons.forEach(btn => {
        btn.classList.remove('active', 'bg-gray-700', 'text-white');
        btn.classList.add('bg-gray-600', 'text-gray-300');
    });
    
    const activeBtn = document.querySelector(`[data-size="${size}"]`);
    activeBtn.classList.remove('bg-gray-600', 'text-gray-300');
    activeBtn.classList.add('active', 'bg-gray-700', 'text-white');
    
    // Update container size
    switch(size) {
        case 'desktop':
            container.style.maxWidth = '100%';
            container.style.margin = '0';
            frame.style.height = '800px';
            break;
        case 'tablet':
            container.style.maxWidth = '768px';
            container.style.margin = '0 auto';
            frame.style.height = '600px';
            break;
        case 'mobile':
            container.style.maxWidth = '375px';
            container.style.margin = '0 auto';
            frame.style.height = '600px';
            break;
    }
}

// Auto-resize iframe based on content
document.addEventListener('DOMContentLoaded', function() {
    const iframe = document.getElementById('preview-frame');
    
    iframe.addEventListener('load', function() {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            const height = Math.max(
                iframeDoc.body.scrollHeight,
                iframeDoc.body.offsetHeight,
                iframeDoc.documentElement.clientHeight,
                iframeDoc.documentElement.scrollHeight,
                iframeDoc.documentElement.offsetHeight
            );
            
            if (height > 0) {
                iframe.style.height = Math.min(height + 50, 1200) + 'px';
            }
        } catch (e) {
            // Cross-origin restrictions, keep default height
            console.log('Cannot access iframe content for auto-resize');
        }
    });
});
</script>

<style>
.responsive-btn.active {
    background-color: #374151 !important;
    color: white !important;
}

#preview-container {
    transition: max-width 0.3s ease, margin 0.3s ease;
}

/* Custom scrollbar for placeholders */
.max-h-64::-webkit-scrollbar {
    width: 6px;
}

.max-h-64::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 3px;
}

.max-h-64::-webkit-scrollbar-thumb {
    background: #6B7280;
    border-radius: 3px;
}

.max-h-64::-webkit-scrollbar-thumb:hover {
    background: #9CA3AF;
}
</style>
@endsection
