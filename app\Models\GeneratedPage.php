<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class GeneratedPage extends Model
{
    protected $fillable = [
        'campaign_id',
        'template_id',
        'title',
        'slug',
        'raw_html',
        'html_with_shortcodes',
        'page_data',
        'seo_data',
        'shortcode_mapping',
        'wordpress_post_id',
        'status',
    ];

    protected $casts = [
        'page_data' => 'array',
        'seo_data' => 'array',
        'shortcode_mapping' => 'array',
    ];

    /**
     * Get the campaign that owns the generated page.
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Get the template that owns the generated page.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class);
    }

    /**
     * Generate slug from title.
     */
    public function generateSlug(): string
    {
        $baseSlug = Str::slug($this->title);
        $slug = $baseSlug;
        $counter = 1;

        while (self::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Generate shortcodes in HTML content.
     */
    public function generateShortcodes(): string
    {
        $html = $this->raw_html;
        $shortcodeMapping = [];

        // Replace pricing section with shortcode
        if (strpos($html, 'id="pricing"') !== false) {
            $shortcodeMapping['price'] = $this->extractPricingContent();
            $html = preg_replace('/<section[^>]*id="pricing"[^>]*>.*?<\/section>/s', '[price]', $html);
        }

        // Replace contact info with shortcodes
        if (isset($this->page_data['Contact'])) {
            $shortcodeMapping['contact'] = $this->page_data['Contact'];
            $html = str_replace($this->page_data['Contact'], '[contact]', $html);
        }

        // Replace address with shortcodes
        if (isset($this->page_data['Address'])) {
            $shortcodeMapping['address'] = $this->page_data['Address'];
            $html = str_replace($this->page_data['Address'], '[address]', $html);
        }

        $this->shortcode_mapping = $shortcodeMapping;
        $this->html_with_shortcodes = $html;

        return $html;
    }

    /**
     * Extract pricing content from HTML.
     */
    private function extractPricingContent(): string
    {
        // This would extract the pricing section content
        // For now, return a default pricing structure
        return '<div class="pricing-section">Default pricing content</div>';
    }

    /**
     * Generate business schema markup.
     */
    public function generateBusinessSchema(): array
    {
        $campaign = $this->campaign;

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'LocalBusiness',
            'name' => $campaign->business_name,
            'description' => $campaign->business_description,
        ];

        if ($campaign->contact_phone) {
            $schema['telephone'] = $campaign->contact_phone;
        }

        if ($campaign->contact_email) {
            $schema['email'] = $campaign->contact_email;
        }

        if ($campaign->address) {
            $schema['address'] = [
                '@type' => 'PostalAddress',
                'streetAddress' => $campaign->address
            ];
        }

        return $schema;
    }

    /**
     * Get combined schema markup (FAQ + Business).
     */
    public function getCombinedSchema(): array
    {
        $schemas = [];

        // Add FAQ schema if FAQ data exists
        if (isset($this->page_data['FAQ']) && is_array($this->page_data['FAQ'])) {
            $schemas[] = $this->template->generateFaqSchema($this->page_data['FAQ']);
        }

        // Add business schema
        $schemas[] = $this->generateBusinessSchema();

        return $schemas;
    }
}
