<?php
/**
 * Image Generator Module for Page Generator Integration
 *
 * This module generates SEO-optimized images with text overlays using GD Library
 * to provide featured images for landing pages.
 */

if (!defined('ABSPATH')) {
    exit;
}

class PageGenerator_Image_Generator {
    
    /**
     * Image dimensions
     */
    private $width = 1200;
    private $height = 630;
    
    /**
     * Font settings
     */
    private $font_size = 48;
    private $font_file;
    
    /**
     * Colors
     */
    private $background_colors = [
        ['r' => 59, 'g' => 130, 'b' => 246],  // Blue
        ['r' => 16, 'g' => 185, 'b' => 129],  // Green
        ['r' => 139, 'g' => 92, 'b' => 246],  // Purple
        ['r' => 245, 'g' => 158, 'b' => 11],  // Orange
        ['r' => 236, 'g' => 72, 'b' => 153],  // Pink
    ];

    /**
     * Plugin settings
     */
    private $settings = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        // Get settings from admin
        $this->settings = get_option('page_generator_options', array());

        // Set font file path - try to use system fonts first, fallback to default
        $this->font_file = $this->get_font_path();

        // Add custom background color from settings if available
        if (!empty($this->settings['image_background_color'])) {
            $custom_color = $this->hex_to_rgb($this->settings['image_background_color']);
            if ($custom_color) {
                array_unshift($this->background_colors, $custom_color);
            }
        }

        // Hook into post save to auto-generate featured images
        add_action('save_post', array($this, 'auto_generate_featured_image'), 10, 2);
    }
    
    /**
     * Get available font path
     */
    private function get_font_path() {
        // Get font style from settings
        $font_style = $this->settings['default_font_style'] ?? 'arial';

        // Map font styles to system font paths
        $font_maps = array(
            'arial' => array(
                '/Windows/Fonts/arial.ttf',
                '/Windows/Fonts/Arial.ttf',
                '/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf',
                '/System/Library/Fonts/Arial.ttf'
            ),
            'helvetica' => array(
                '/Windows/Fonts/calibri.ttf',
                '/System/Library/Fonts/Helvetica.ttc',
                '/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf'
            ),
            'times' => array(
                '/Windows/Fonts/times.ttf',
                '/Windows/Fonts/Times.ttf',
                '/System/Library/Fonts/Times.ttc',
                '/usr/share/fonts/truetype/liberation/LiberationSerif-Bold.ttf'
            ),
            'georgia' => array(
                '/Windows/Fonts/georgia.ttf',
                '/Windows/Fonts/Georgia.ttf',
                '/System/Library/Fonts/Georgia.ttf'
            ),
            'verdana' => array(
                '/Windows/Fonts/verdana.ttf',
                '/Windows/Fonts/Verdana.ttf',
                '/System/Library/Fonts/Verdana.ttf'
            ),
            'trebuchet' => array(
                '/Windows/Fonts/trebuc.ttf',
                '/Windows/Fonts/Trebuchet.ttf'
            ),
            'impact' => array(
                '/Windows/Fonts/impact.ttf',
                '/Windows/Fonts/Impact.ttf'
            ),
            'comic' => array(
                '/Windows/Fonts/comic.ttf',
                '/Windows/Fonts/Comic.ttf'
            )
        );

        // Try fonts for selected style
        if (isset($font_maps[$font_style])) {
            foreach ($font_maps[$font_style] as $font) {
                if (file_exists($font)) {
                    return $font;
                }
            }
        }

        // Fallback to any available font
        $all_fonts = array_merge(...array_values($font_maps));
        foreach ($all_fonts as $font) {
            if (file_exists($font)) {
                return $font;
            }
        }

        // Fallback to default GD font
        return null;
    }
    
    /**
     * Generate image with text overlay
     */
    public function generate_image($text, $keyword = '', $filename = null) {
        // Use the enhanced method with better keyword integration
        return $this->generate_image_with_keywords($text, $keyword, $filename);
    }
    
    /**
     * Add gradient effect to background
     */
    private function add_gradient($image, $base_color) {
        // Create darker version for gradient
        $darker_color = imagecolorallocate(
            $image,
            max(0, min(255, $base_color['r'] - 30)),
            max(0, min(255, $base_color['g'] - 30)),
            max(0, min(255, $base_color['b'] - 30))
        );
        
        // Add gradient from top to bottom
        for ($y = 0; $y < $this->height; $y++) {
            $alpha = 1 - ($y / $this->height);
            $color = imagecolorallocate(
                $image,
                max(0, min(255, (int)($base_color['r'] * $alpha + ($base_color['r'] - 30) * (1 - $alpha)))),
                max(0, min(255, (int)($base_color['g'] * $alpha + ($base_color['g'] - 30) * (1 - $alpha)))),
                max(0, min(255, (int)($base_color['b'] * $alpha + ($base_color['b'] - 30) * (1 - $alpha))))
            );
            
            imageline($image, 0, $y, $this->width, $y, $color);
        }
    }
    
    /**
     * Add text to image
     */
    private function add_text($image, $text, $text_color) {
        // Clean and truncate text
        $text = function_exists('sanitize_text_field') ?
            sanitize_text_field($text) :
            trim(strip_tags($text));
        $text = $this->truncate_text($text, 50); // Max 50 characters
        
        // Calculate text positioning
        $font_size = $this->font_size;
        $text_box = $this->calculate_text_box($text, $font_size);
        
        $x = (int)(($this->width - $text_box['width']) / 2);
        $y = (int)(($this->height - $text_box['height']) / 2 + $text_box['height']);
        
        // Add text shadow
        $shadow_color = imagecolorallocate($image, 0, 0, 0);
        $this->add_text_with_shadow($image, $text, $x, $y, $font_size, $text_color, $shadow_color);
    }
    
    /**
     * Add text with shadow effect
     */
    private function add_text_with_shadow($image, $text, $x, $y, $font_size, $text_color, $shadow_color) {
        if ($this->font_file && file_exists($this->font_file)) {
            // Use TrueType font if available
            imagettftext($image, $font_size, 0, (int)($x + 2), (int)($y + 2), $shadow_color, $this->font_file, $text);
            imagettftext($image, $font_size, 0, (int)$x, (int)$y, $text_color, $this->font_file, $text);
        } else {
            // Fallback to default GD font
            $font = 5; // Largest built-in font
            imagestring($image, $font, (int)($x + 1), (int)($y + 1), $text, $shadow_color);
            imagestring($image, $font, (int)$x, (int)$y, $text, $text_color);
        }
    }
    
    /**
     * Calculate text bounding box
     */
    private function calculate_text_box($text, $font_size) {
        if ($this->font_file && file_exists($this->font_file)) {
            $bbox = imagettfbbox($font_size, 0, $this->font_file, $text);
            return [
                'width' => abs($bbox[4] - $bbox[0]),
                'height' => abs($bbox[5] - $bbox[1])
            ];
        } else {
            // Estimate for default font
            $width = strlen($text) * 8;
            $height = 16;
            return ['width' => $width, 'height' => $height];
        }
    }
    
    /**
     * Add decorative elements
     */
    private function add_decorative_elements($image, $base_color) {
        // Add some geometric shapes
        $shape_color = imagecolorallocatealpha(
            $image,
            255,
            255,
            255,
            30 // Transparency (0-127, where 0 is opaque and 127 is transparent)
        );
        
        // Add circles or rectangles as decorative elements
        for ($i = 0; $i < 3; $i++) {
            $x = rand(50, $this->width - 50);
            $y = rand(50, $this->height - 50);
            $size = rand(20, 60);
            
            if (rand(0, 1)) {
                imagefilledellipse($image, $x, $y, $size, $size, $shape_color);
            } else {
                imagefilledrectangle($image, $x, $y, $x + $size, $y + $size, $shape_color);
            }
        }
    }
    
    /**
     * Truncate text to fit image
     */
    private function truncate_text($text, $max_length) {
        if (strlen($text) <= $max_length) {
            return $text;
        }
        return substr($text, 0, $max_length - 3) . '...';
    }
    
    /**
     * Generate ALT text for image
     */
    private function generate_alt_text($text, $keyword) {
        if ($keyword) {
            $alt_text = $keyword . ' services - ' . $text;
        } else {
            $alt_text = 'Professional services - ' . $text;
        }

        return function_exists('sanitize_text_field') ?
            sanitize_text_field($alt_text) :
            trim(strip_tags($alt_text));
    }
    
    /**
     * Generate badge image (smaller size for badges)
     */
    public function generate_badge_image($text, $filename = null) {
        try {
            error_log('Rife PG Image: Starting badge image generation for text: ' . $text);

            // Validate input
            if (empty($text)) {
                throw new Exception('Text parameter is empty');
            }

            // Check if GD library is available
            if (!extension_loaded('gd')) {
                throw new Exception('GD library is not available on this server');
            }

            // Badge dimensions (smaller than regular images)
            $badge_width = 300;
            $badge_height = 100;

            // Create image
            $image = imagecreatetruecolor($badge_width, $badge_height);
            if (!$image) {
                throw new Exception('Failed to create image resource');
            }

            // Select background color (use first color for consistency)
            $bg_color = $this->background_colors[0]; // Blue
            $background = imagecolorallocate($image, $bg_color['r'], $bg_color['g'], $bg_color['b']);
            imagefill($image, 0, 0, $background);

            // Add text (smaller font for badge)
            $text_color = imagecolorallocate($image, 255, 255, 255);
            $badge_font_size = 20;

            // Calculate text position (center)
            if ($this->font_file && file_exists($this->font_file)) {
                $text_box = imagettfbbox($badge_font_size, 0, $this->font_file, $text);
                $text_width = $text_box[4] - $text_box[0];
                $text_height = $text_box[1] - $text_box[7];

                $x = (int)(($badge_width - $text_width) / 2);
                $y = (int)(($badge_height - $text_height) / 2 + $text_height);

                // Add text to image
                imagettftext($image, $badge_font_size, 0, $x, $y, $text_color, $this->font_file, $text);
            } else {
                // Fallback to default GD font
                $font = 5; // Largest built-in font
                $text_width = strlen($text) * 10; // Estimate width
                $text_height = 15; // Estimate height

                $x = (int)(($badge_width - $text_width) / 2);
                $y = (int)(($badge_height - $text_height) / 2);

                imagestring($image, $font, $x, $y, $text, $text_color);
            }

            // Generate filename if not provided
            if (!$filename) {
                $safe_name = function_exists('sanitize_title') ?
                    sanitize_title($text) :
                    preg_replace('/[^a-z0-9-]/', '-', strtolower($text));
                $filename = 'rife-pg-badge-' . $safe_name . '-' . time() . '.jpg';
            }

            if (function_exists('wp_upload_dir')) {
                $upload_dir = wp_upload_dir();
                $image_path = $upload_dir['path'] . '/' . $filename;
                $image_url = $upload_dir['url'] . '/' . $filename;
            } else {
                // Fallback for testing
                $temp_dir = sys_get_temp_dir();
                $image_path = $temp_dir . DIRECTORY_SEPARATOR . $filename;
                $image_url = 'file://' . $image_path;
            }

            // Save image
            $saved = imagejpeg($image, $image_path, 90);
            if (!$saved) {
                throw new Exception('Failed to save badge image to: ' . $image_path);
            }

            // Clean up
            imagedestroy($image);

            error_log('Rife PG Image: Badge image generated successfully: ' . $image_url);

            return [
                'success' => true,
                'path' => $image_path,
                'url' => $image_url,
                'filename' => $filename,
                'alt_text' => $text
            ];

        } catch (Exception $e) {
            error_log('Rife PG Image: Badge generation error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate multiple images for different sections
     */
    public function generate_image_set($keyword, $section_type = 'general') {
        $images = [];

        // Generate different types of images based on section
        $texts = $this->get_section_texts($keyword, $section_type);

        foreach ($texts as $index => $text) {
            $filename = 'rife-pg-' . sanitize_title($section_type) . '-' . $index . '-' . time() . '.jpg';
            $result = $this->generate_image($text, $keyword, $filename);

            if ($result['success']) {
                $images[] = $result;
            }
        }

        return $images;
    }
    
    /**
     * Get appropriate text for different sections
     */
    private function get_section_texts($keyword, $section_type) {
        $texts = [];
        
        switch ($section_type) {
            case 'hero':
                $texts = [
                    $keyword,
                    'Professional ' . $keyword,
                    $keyword . ' Solutions'
                ];
                break;
            case 'benefits':
                $texts = [
                    'Why Choose ' . $keyword,
                    $keyword . ' Benefits',
                    'Our ' . $keyword . ' Advantage'
                ];
                break;
            case 'process':
                $texts = [
                    $keyword . ' Process',
                    'How ' . $keyword . ' Works',
                    $keyword . ' Methodology'
                ];
                break;
            case 'testimonials':
                $texts = [
                    'Client Success',
                    'Happy Customers',
                    'Proven Results'
                ];
                break;
            default:
                $texts = [
                    $keyword . ' Services',
                    'Professional ' . $keyword,
                    'Expert ' . $keyword
                ];
        }
        
        return $texts;
    }
    
    /**
     * Set image as featured image for post
     */
    public function set_featured_image($post_id, $image_path, $image_url, $alt_text) {
        try {
            error_log('Rife PG Image: Setting featured image for post ' . $post_id . ' from: ' . $image_path);
            
            // Validate input
            if (empty($post_id) || empty($image_path) || empty($image_url)) {
                throw new Exception('Missing required parameters for featured image');
            }
            
            // Check if file exists
            if (!file_exists($image_path)) {
                throw new Exception('Image file does not exist: ' . $image_path);
            }
            
            require_once(ABSPATH . 'wp-admin/includes/image.php');
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/media.php');
            
            // Check if image already exists in media library
            $existing_image = $this->find_existing_image($image_url);
            if ($existing_image) {
                error_log('Rife PG Image: Using existing image: ' . $existing_image);
                return set_post_thumbnail($post_id, $existing_image);
            }
            
            // Upload image to media library
            $attachment = [
                'post_mime_type' => 'image/jpeg',
                'post_title' => function_exists('sanitize_file_name') ?
                    sanitize_file_name(basename($image_path)) :
                    basename($image_path),
                'post_content' => '',
                'post_status' => 'inherit'
            ];
            
            $attach_id = wp_insert_attachment($attachment, $image_path, $post_id);
            
            if (is_wp_error($attach_id)) {
                throw new Exception('Failed to create attachment: ' . $attach_id->get_error_message());
            }
            
            // Generate metadata and update attachment
            $attach_data = wp_generate_attachment_metadata($attach_id, $image_path);
            if (is_wp_error($attach_data)) {
                throw new Exception('Failed to generate attachment metadata: ' . $attach_data->get_error_message());
            }
            
            $updated = wp_update_attachment_metadata($attach_id, $attach_data);
            if (!$updated) {
                error_log('Rife PG Image: Warning - Failed to update attachment metadata');
            }
            
            // Set ALT text
            $alt_updated = update_post_meta($attach_id, '_wp_attachment_image_alt', $alt_text);
            if (!$alt_updated) {
                error_log('Rife PG Image: Warning - Failed to set ALT text');
            }
            
            // Set as featured image
            $result = set_post_thumbnail($post_id, $attach_id);
            if ($result) {
                error_log('Rife PG Image: Successfully set featured image for post ' . $post_id);
            } else {
                error_log('Rife PG Image: Failed to set featured image for post ' . $post_id);
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log('Rife PG Image: Featured image error - ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find existing image in media library
     */
    private function find_existing_image($image_url) {
        global $wpdb;
        
        $filename = basename($image_url);
        $query = $wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} 
             WHERE meta_key = '_wp_attached_file' 
             AND meta_value LIKE %s",
            '%' . $filename
        );
        
        $existing_id = $wpdb->get_var($query);
        return $existing_id ? $existing_id : false;
    }

    /**
     * Convert hex color to RGB array
     */
    private function hex_to_rgb($hex) {
        // Remove # if present
        $hex = ltrim($hex, '#');

        // Check if valid hex
        if (strlen($hex) != 6) {
            return false;
        }

        // Convert to RGB
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));

        return array('r' => $r, 'g' => $g, 'b' => $b);
    }

    /**
     * Apply custom background image to the canvas
     */
    private function apply_custom_background($image) {
        $background_url = $this->settings['default_background_image'];
        $overlay_opacity = $this->settings['background_overlay_opacity'] ?? 70;

        try {
            // Download and load background image
            $background_data = file_get_contents($background_url);
            if ($background_data === false) {
                throw new Exception('Failed to download background image');
            }

            $background_image = imagecreatefromstring($background_data);
            if (!$background_image) {
                throw new Exception('Failed to create image from background data');
            }

            // Resize background to fit canvas
            $bg_width = imagesx($background_image);
            $bg_height = imagesy($background_image);

            // Calculate scaling to cover the entire canvas
            $scale_x = $this->width / $bg_width;
            $scale_y = $this->height / $bg_height;
            $scale = max($scale_x, $scale_y); // Use max to cover entire area

            $new_width = (int)($bg_width * $scale);
            $new_height = (int)($bg_height * $scale);

            // Center the background
            $offset_x = (int)(($this->width - $new_width) / 2);
            $offset_y = (int)(($this->height - $new_height) / 2);

            // Copy and resize background to canvas
            imagecopyresampled(
                $image, $background_image,
                $offset_x, $offset_y, 0, 0,
                $new_width, $new_height, $bg_width, $bg_height
            );

            // Add dark overlay for text readability
            if ($overlay_opacity > 0) {
                $overlay_alpha = (int)(127 - ($overlay_opacity / 100 * 127));
                $overlay_color = imagecolorallocatealpha($image, 0, 0, 0, $overlay_alpha);
                imagefill($image, 0, 0, $overlay_color);
            }

            // Clean up
            imagedestroy($background_image);

            error_log('Rife PG Image: Custom background applied successfully');

        } catch (Exception $e) {
            error_log('Rife PG Image: Failed to apply custom background: ' . $e->getMessage());

            // Fallback to solid color background
            $bg_color = $this->background_colors[0]; // Use first color as fallback
            $background = imagecolorallocate($image, $bg_color['r'], $bg_color['g'], $bg_color['b']);
            imagefill($image, 0, 0, $background);
            $this->add_gradient($image, $bg_color);
        }
    }

    /**
     * Auto-generate featured image for landing pages
     */
    public function auto_generate_featured_image($post_id, $post) {
        // Only process landing_page post type
        if ($post->post_type !== 'landing_page') {
            return;
        }

        // Skip if post already has featured image
        if (has_post_thumbnail($post_id)) {
            return;
        }

        // Skip if this is an autosave or revision
        if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
            return;
        }

        // Get post title and extract keywords
        $title = get_the_title($post_id);
        $keywords = $this->extract_keywords_from_title($title);

        if (empty($keywords)) {
            $keywords = $title; // Fallback to full title
        }

        // Generate image with keywords using enhanced method
        $image_result = $this->generate_image_with_keywords($title, $keywords);

        if ($image_result['success']) {
            // Set as featured image
            $this->set_featured_image($post_id, $image_result['path'], $image_result['url'], $image_result['alt_text']);

            error_log('PageGenerator Image: Auto-generated featured image for post ' . $post_id . ' with keywords: ' . $keywords);
        } else {
            error_log('PageGenerator Image: Failed to auto-generate featured image for post ' . $post_id . ': ' . $image_result['error']);
        }
    }

    /**
     * Extract keywords from post title
     */
    private function extract_keywords_from_title($title) {
        // Remove common stop words
        $stop_words = array('the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those');

        // Clean title and split into words
        $title = strtolower(trim(strip_tags($title)));
        $words = preg_split('/\s+/', $title);

        // Filter out stop words and short words
        $keywords = array_filter($words, function($word) use ($stop_words) {
            return strlen($word) > 2 && !in_array($word, $stop_words);
        });

        // Return first 3 keywords joined
        $keywords = array_slice($keywords, 0, 3);
        return implode(' ', $keywords);
    }

    /**
     * Generate ALT text with keywords for SEO
     */
    private function generate_seo_alt_text($title, $keywords) {
        // Create SEO-optimized ALT text
        $alt_parts = array();

        if (!empty($keywords)) {
            $alt_parts[] = $keywords;
        }

        $alt_parts[] = 'professional services';
        $alt_parts[] = 'featured image';

        // Add location if available in title
        if (preg_match('/\b(in|at|near)\s+([a-zA-Z\s]+)/i', $title, $matches)) {
            $location = trim($matches[2]);
            if (strlen($location) > 2) {
                $alt_parts[] = 'in ' . $location;
            }
        }

        $alt_text = implode(' - ', $alt_parts);

        // Clean and limit length
        $alt_text = preg_replace('/[^a-zA-Z0-9\s\-]/', '', $alt_text);
        $alt_text = substr($alt_text, 0, 125); // Keep under 125 chars for SEO

        return trim($alt_text);
    }

    /**
     * Enhanced generate_image method with better keyword integration
     */
    public function generate_image_with_keywords($text, $keywords = '', $filename = null) {
        try {
            error_log('PageGenerator Image: Starting image generation with keywords - Text: ' . $text . ', Keywords: ' . $keywords);

            // Validate input
            if (empty($text)) {
                throw new Exception('Text parameter is empty');
            }

            // Check if GD library is available
            if (!extension_loaded('gd')) {
                throw new Exception('GD library is not available on this server');
            }

            // Create image resource
            $image = imagecreatetruecolor($this->width, $this->height);
            if (!$image) {
                throw new Exception('Failed to create image resource');
            }

            // Select background color based on keywords
            $bg_color = $this->select_color_by_keywords($keywords);
            $background = imagecolorallocate($image, $bg_color['r'], $bg_color['g'], $bg_color['b']);
            imagefill($image, 0, 0, $background);

            // Add gradient effect
            $this->add_gradient($image, $bg_color);

            // Add main text (title)
            $text_color = imagecolorallocate($image, 255, 255, 255);
            $this->add_main_text($image, $text, $text_color);

            // Add keywords as subtitle if different from main text
            if (!empty($keywords) && $keywords !== $text) {
                $this->add_keyword_text($image, $keywords, $text_color);
            }

            // Add decorative elements
            $this->add_decorative_elements($image, $bg_color);

            // Generate filename if not provided
            if (!$filename) {
                $safe_name = sanitize_title($keywords ?: $text);
                $filename = 'pg-featured-' . $safe_name . '-' . time() . '.jpg';
            }

            $upload_dir = wp_upload_dir();
            $image_path = $upload_dir['path'] . '/' . $filename;
            $image_url = $upload_dir['url'] . '/' . $filename;

            // Save image
            $saved = imagejpeg($image, $image_path, 90);
            if (!$saved) {
                throw new Exception('Failed to save image to: ' . $image_path);
            }

            // Clean up
            imagedestroy($image);

            error_log('PageGenerator Image: Image generated successfully with keywords: ' . $image_url);

            return [
                'success' => true,
                'path' => $image_path,
                'url' => $image_url,
                'filename' => $filename,
                'alt_text' => $this->generate_seo_alt_text($text, $keywords)
            ];

        } catch (Exception $e) {
            error_log('PageGenerator Image: Generation error - ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Select background color based on keywords
     */
    private function select_color_by_keywords($keywords) {
        if (empty($keywords)) {
            return $this->background_colors[0]; // Default blue
        }

        $keywords_lower = strtolower($keywords);

        // Map keywords to colors
        if (strpos($keywords_lower, 'health') !== false || strpos($keywords_lower, 'medical') !== false) {
            return ['r' => 16, 'g' => 185, 'b' => 129]; // Green for health
        } elseif (strpos($keywords_lower, 'tech') !== false || strpos($keywords_lower, 'digital') !== false) {
            return ['r' => 139, 'g' => 92, 'b' => 246]; // Purple for tech
        } elseif (strpos($keywords_lower, 'business') !== false || strpos($keywords_lower, 'finance') !== false) {
            return ['r' => 59, 'g' => 130, 'b' => 246]; // Blue for business
        } elseif (strpos($keywords_lower, 'creative') !== false || strpos($keywords_lower, 'design') !== false) {
            return ['r' => 236, 'g' => 72, 'b' => 153]; // Pink for creative
        } elseif (strpos($keywords_lower, 'food') !== false || strpos($keywords_lower, 'restaurant') !== false) {
            return ['r' => 245, 'g' => 158, 'b' => 11]; // Orange for food
        }

        // Default to first color
        return $this->background_colors[0];
    }

    /**
     * Add main text to image
     */
    private function add_main_text($image, $text, $text_color) {
        // Clean and truncate text
        $text = sanitize_text_field($text);
        $text = $this->truncate_text($text, 40); // Max 40 characters for main text

        // Calculate text positioning (upper center)
        $font_size = $this->font_size;
        $text_box = $this->calculate_text_box($text, $font_size);

        $x = (int)(($this->width - $text_box['width']) / 2);
        $y = (int)($this->height * 0.4); // Position at 40% from top

        // Add text shadow
        $shadow_color = imagecolorallocate($image, 0, 0, 0);
        $this->add_text_with_shadow($image, $text, $x, $y, $font_size, $text_color, $shadow_color);
    }

    /**
     * Add keyword text as subtitle
     */
    private function add_keyword_text($image, $keywords, $text_color) {
        // Clean keywords
        $keywords = sanitize_text_field($keywords);
        $keywords = $this->truncate_text($keywords, 30); // Max 30 characters for keywords

        // Smaller font for keywords
        $keyword_font_size = (int)($this->font_size * 0.6);
        $text_box = $this->calculate_text_box($keywords, $keyword_font_size);

        $x = (int)(($this->width - $text_box['width']) / 2);
        $y = (int)($this->height * 0.6); // Position at 60% from top

        // Add semi-transparent background for keywords
        $keyword_bg = imagecolorallocatealpha($image, 0, 0, 0, 50);
        $padding = 10;
        imagefilledrectangle(
            $image,
            $x - $padding,
            $y - $text_box['height'] - $padding,
            $x + $text_box['width'] + $padding,
            $y + $padding,
            $keyword_bg
        );

        // Add keyword text
        $shadow_color = imagecolorallocate($image, 0, 0, 0);
        $this->add_text_with_shadow($image, $keywords, $x, $y, $keyword_font_size, $text_color, $shadow_color);
    }
}
?>