<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Campaign;
use App\Models\Template;
use App\Models\GeneratedPage;
use App\Services\ContentGeneratorService;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class CampaignController extends Controller
{
    protected ContentGeneratorService $contentGeneratorService;

    public function __construct(ContentGeneratorService $contentGeneratorService)
    {
        $this->contentGeneratorService = $contentGeneratorService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        $campaigns = Campaign::with(['generatedPages', 'exportLogs'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('campaigns.index', compact('campaigns'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $templates = Template::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        $wordpressConnections = \App\Models\WordPressConnection::active()->get();

        return view('campaigns.create', compact('templates', 'wordpressConnections'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'keywords' => 'required|array|min:1',
            'keywords.*' => 'required|string|max:255',
            'business_name' => 'required|string|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'contact_email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'business_description' => 'nullable|string',
            'keyword_faqs' => 'nullable|array',
            'keyword_faqs.*' => 'nullable|array',
            'keyword_faqs.*.*' => 'string',
            'wordpress_connection_id' => 'nullable|exists:word_press_connections,id',
            'template_id' => 'nullable|exists:templates,id',
        ]);

        // Process keywords and FAQ data
        $keywords = $validated['keywords'];
        $keywordFaqs = $validated['keyword_faqs'] ?? [];

        // Create structured data for keywords and their FAQs
        $keywordData = [];
        foreach ($keywords as $index => $keyword) {
            $keywordData[] = [
                'keyword' => $keyword,
                'faqs' => array_filter($keywordFaqs[$index] ?? [])
            ];
        }

        // Use first keyword as main_keyword for backward compatibility
        $campaignData = $validated;
        $campaignData['main_keyword'] = $keywords[0];
        $campaignData['keywords_data'] = $keywordData;

        // Combine all FAQ questions for backward compatibility
        $allFaqs = [];
        foreach ($keywordFaqs as $faqs) {
            $allFaqs = array_merge($allFaqs, array_filter($faqs));
        }
        $campaignData['people_also_ask'] = $allFaqs;

        // Remove the arrays that are not in the database schema
        unset($campaignData['keywords'], $campaignData['keyword_faqs']);

        $campaign = Campaign::create($campaignData);

        // Generate master prompt using service
        $masterPrompt = $this->contentGeneratorService->generateMasterPrompt($campaign);
        $campaign->update(['master_prompt' => $masterPrompt]);

        return redirect()->route('campaigns.show', $campaign)
            ->with('success', 'Campaign berhasil dibuat!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Campaign $campaign): View
    {
        $campaign->load(['generatedPages.template']);

        // Paginate the export logs, ordering by the newest first.
        $exportLogs = $campaign->exportLogs()->orderBy('created_at', 'desc')->paginate(5);

        return view('campaigns.show', compact('campaign', 'exportLogs'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Campaign $campaign): View
    {
        $templates = Template::where('is_active', true)
            ->orderBy('sort_order')
            ->get();
        $wordpressConnections = \App\Models\WordPressConnection::active()->get();
        return view('campaigns.edit', compact('campaign', 'templates', 'wordpressConnections'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Campaign $campaign): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'main_keyword' => 'required|string|max:255',
            'business_name' => 'required|string|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'contact_email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'business_description' => 'nullable|string',
            'people_also_ask' => 'nullable|array',
            'people_also_ask.*' => 'string',
            'status' => 'required|in:draft,active,completed,archived',
            'wordpress_connection_id' => 'nullable|exists:word_press_connections,id',
            'template_id' => 'nullable|exists:templates,id',
        ]);

        $campaign->update($validated);

        // Regenerate master prompt if campaign data changed using service
        $masterPrompt = $this->contentGeneratorService->generateMasterPrompt($campaign);
        $campaign->update(['master_prompt' => $masterPrompt]);

        return redirect()->route('campaigns.show', $campaign)
            ->with('success', 'Campaign berhasil diupdate!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Campaign $campaign): RedirectResponse
    {
        $campaign->delete();

        return redirect()->route('campaigns.index')
            ->with('success', 'Campaign berhasil dihapus!');
    }

    /**
     * Show master prompt for the campaign.
     */
    public function showMasterPrompt(Campaign $campaign): View
    {
        // Load the campaign's assigned template
        $campaign->load('template');
        $template = $campaign->template;

        if ($template && $template->is_processed) {
            // Use combined master prompt with template and campaign context
            $masterPrompt = $campaign->generateCombinedMasterPrompt($template);
        } else {
            // Fallback to campaign master prompt or generate new one
            $masterPrompt = $campaign->generateMasterPrompt();
        }

        return view('campaigns.master-prompt', compact('campaign', 'masterPrompt', 'template'));
    }

    /**
     * Delete a single generated page
     */
    public function deletePage(Campaign $campaign, GeneratedPage $page): RedirectResponse
    {
        // Ensure the page belongs to this campaign
        if ($page->campaign_id !== $campaign->id) {
            return redirect()->route('campaigns.show', $campaign)
                ->with('error', 'Page not found in this campaign.');
        }

        $page->delete();

        return redirect()->route('campaigns.show', $campaign)
            ->with('success', 'Page deleted successfully.');
    }

    /**
     * Delete selected generated pages
     */
    public function deleteSelectedPages(Request $request, Campaign $campaign): RedirectResponse
    {
        $validated = $request->validate([
            'page_ids' => 'required|array',
            'page_ids.*' => 'exists:generated_pages,id'
        ]);

        $deletedCount = GeneratedPage::whereIn('id', $validated['page_ids'])
            ->where('campaign_id', $campaign->id)
            ->delete();

        return redirect()->route('campaigns.show', $campaign)
            ->with('success', "Successfully deleted {$deletedCount} page(s).");
    }

    /**
     * Delete all generated pages for a campaign
     */
    public function deleteAllPages(Campaign $campaign): RedirectResponse
    {
        $deletedCount = $campaign->generatedPages()->delete();

        return redirect()->route('campaigns.show', $campaign)
            ->with('success', "Successfully deleted all {$deletedCount} generated page(s).");
    }
}
