<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

echo '=== PERBANDINGAN POST MANUAL vs LARAVEL EXPORT ===' . PHP_EOL . PHP_EOL;

// Post manual (yang dibuat di WordPress admin)
$manual_post_id = 930;
echo '1. POST MANUAL (ID: ' . $manual_post_id . ') - Test Shortcode Landing Page' . PHP_EOL;
echo '   Dibuat manual di WordPress admin' . PHP_EOL;

$manual_post = get_post($manual_post_id);
echo '   Post Content: "' . substr($manual_post->post_content, 0, 100) . '..."' . PHP_EOL;

$manual_page_data = get_post_meta($manual_post_id, 'page_data', true);
$manual_source = get_post_meta($manual_post_id, 'page_generator_source', true);

echo '   Meta Fields:' . PHP_EOL;
echo '   - page_data: ' . ($manual_page_data ? 'ADA' : 'TIDAK ADA') . PHP_EOL;
echo '   - page_generator_source: ' . ($manual_source ? $manual_source : 'TIDAK ADA') . PHP_EOL;

echo PHP_EOL . '   TEMPLATE AKAN MENGGUNAKAN: the_content() (fallback terakhir)' . PHP_EOL;
echo '   HASIL: Edit di WordPress admin AKAN BERPENGARUH' . PHP_EOL . PHP_EOL;

// Post dari Laravel export
$laravel_post_id = 932;
echo '2. POST LARAVEL EXPORT (ID: ' . $laravel_post_id . ') - Jasa Pembuatan Website' . PHP_EOL;
echo '   Di-export dari Laravel via AJAX' . PHP_EOL;

$laravel_post = get_post($laravel_post_id);
echo '   Post Content: "' . substr($laravel_post->post_content, 0, 100) . '..."' . PHP_EOL;

$laravel_page_data = get_post_meta($laravel_post_id, 'page_data', true);
$laravel_source = get_post_meta($laravel_post_id, 'page_generator_source', true);

echo '   Meta Fields:' . PHP_EOL;
echo '   - page_data: ' . ($laravel_page_data ? 'ADA (main_content: ' . strlen($laravel_page_data['main_content']) . ' chars)' : 'TIDAK ADA') . PHP_EOL;
echo '   - page_generator_source: ' . ($laravel_source ? $laravel_source : 'TIDAK ADA') . PHP_EOL;

echo PHP_EOL . '   TEMPLATE AKAN MENGGUNAKAN: page_data[main_content] (prioritas utama)' . PHP_EOL;
echo '   HASIL: Edit di WordPress admin TIDAK BERPENGARUH!' . PHP_EOL . PHP_EOL;

echo '=== KESIMPULAN ===' . PHP_EOL;
echo 'Template single-landing_page.php menggunakan prioritas:' . PHP_EOL;
echo '1. PageGenerator_Template_Handler::render_landing_page_content() (jika ada)' . PHP_EOL;
echo '2. page_data[main_content] dari meta fields (jika ada)' . PHP_EOL;
echo '3. page_generator_main_content dari meta fields (jika ada)' . PHP_EOL;
echo '4. the_content() - post content biasa (fallback terakhir)' . PHP_EOL . PHP_EOL;

echo 'Untuk post dari Laravel export, data disimpan di meta fields,' . PHP_EOL;
echo 'sehingga edit manual di post content tidak berpengaruh!' . PHP_EOL;
?>
