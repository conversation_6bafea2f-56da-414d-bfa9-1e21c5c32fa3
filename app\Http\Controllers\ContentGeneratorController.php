<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Campaign;
use App\Models\Template;
use App\Models\GeneratedPage;
use App\Services\ContentGeneratorService;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;

class ContentGeneratorController extends Controller
{
    protected ContentGeneratorService $contentGeneratorService;

    public function __construct(ContentGeneratorService $contentGeneratorService)
    {
        $this->contentGeneratorService = $contentGeneratorService;
    }

    /**
     * Show content generator interface.
     */
    public function index(): View
    {
        $campaigns = Campaign::where('status', '!=', 'archived')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('content-generator.index', compact('campaigns'));
    }

    /**
     * Show JSON upload form for a campaign.
     */
    public function upload(Campaign $campaign): View
    {
        $templates = Template::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        return view('content-generator.upload', compact('campaign', 'templates'));
    }

    /**
     * Process uploaded JSON file and generate pages.
     */
    public function processJson(Request $request, Campaign $campaign): RedirectResponse
    {
        $request->validate([
            'json_file' => 'required|file|mimes:json,txt',
            'template_id' => 'required|exists:templates,id',
        ]);

        $template = Template::findOrFail($request->template_id);

        try {
            // Read and parse JSON file
            $jsonContent = file_get_contents($request->file('json_file')->getRealPath());
            $jsonData = json_decode($jsonContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return back()->withErrors(['json_file' => 'File JSON tidak valid.']);
            }

            // Validate JSON structure
            $validationErrors = $this->contentGeneratorService->validateJsonStructure($jsonData, $template);
            if (!empty($validationErrors)) {
                return back()->withErrors(['json_file' => implode(', ', $validationErrors)]);
            }

            // Process JSON data
            $results = $this->contentGeneratorService->processJsonData($campaign, $template, $jsonData);

            $message = "Berhasil generate {$results['success']} halaman.";
            if (!empty($results['errors'])) {
                $message .= " Dengan " . count($results['errors']) . " error.";
            }

            return redirect()->route('campaigns.show', $campaign)
                ->with('success', $message)
                ->with('errors', $results['errors']);

        } catch (\Exception $e) {
            return back()->withErrors(['json_file' => 'Error memproses file: ' . $e->getMessage()]);
        }
    }

    /**
     * Preview generated page.
     */
    public function preview(GeneratedPage $page): View
    {
        $previewHtml = $this->contentGeneratorService->previewPage($page);
        return view('content-generator.preview', compact('page', 'previewHtml'));
    }

    /**
     * Preview generated page in new window.
     */
    public function previewWindow(GeneratedPage $page)
    {
        $previewHtml = $this->contentGeneratorService->previewPage($page);
        return response($previewHtml)->header('Content-Type', 'text/html');
    }

    /**
     * Get complete HTML with all assets embedded (for "View HTML" modal and export).
     */
    public function getCompleteHtml(GeneratedPage $page)
    {
        // Get the complete HTML with all assets embedded
        $contentService = app(\App\Services\WordPressContentService::class);
        $completeHtml = $contentService->getCompletePreviewHtml($page);

        return response($completeHtml)->header('Content-Type', 'text/plain');
    }

    /**
     * Get master prompt for campaign via AJAX.
     */
    public function getMasterPrompt(Request $request, Campaign $campaign): JsonResponse
    {
        $templateId = $request->query('template_id');

        if ($templateId) {
            // Use the selected template
            $template = Template::where('id', $templateId)->where('is_active', true)->first();

            if (!$template) {
                return response()->json([
                    'error' => 'Template not found or inactive'
                ], 404);
            }
        } else {
            // No template selected, return read-only message
            return response()->json([
                'prompt' => 'Please select a template first to view the master prompt.',
                'readonly' => true
            ]);
        }

        // Always use the new combined master prompt method
        $prompt = $campaign->generateCombinedMasterPrompt($template);

        return response()->json([
            'prompt' => $prompt,
            'readonly' => false,
            'template_id' => $template->id,
            'template_name' => $template->name
        ]);
    }

    /**
     * Regenerate master prompt for campaign.
     */
    public function regeneratePrompt(Campaign $campaign): JsonResponse
    {
        $masterPrompt = $this->contentGeneratorService->generateMasterPrompt($campaign);
        $campaign->update(['master_prompt' => $masterPrompt]);

        return response()->json([
            'prompt' => $masterPrompt,
            'message' => 'Master prompt berhasil di-regenerate!'
        ]);
    }
}
