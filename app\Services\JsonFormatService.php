<?php

namespace App\Services;

class JsonFormatService
{
    /**
     * Convert AI-generated JSON format to template-compatible format.
     */
    public function convertAiJsonToTemplateFormat(array $data): array
    {
        $converted = $data;
        
        // Convert FAQ format from FAQ_1_question, FAQ_1_answer to FAQ array
        $converted = $this->convertFaqFormat($converted);
        
        // Convert Benefit format from Benefit_1_title, Benefit_1_description to individual placeholders
        $converted = $this->convertBenefitFormat($converted);
        
        // Convert Feature format from Feature_1_title, Feature_1_description to individual placeholders
        $converted = $this->convertFeatureFormat($converted);
        
        return $converted;
    }
    
    /**
     * Convert FAQ format from AI to template format.
     */
    private function convertFaqFormat(array $data): array
    {
        $faqs = [];
        $i = 1;
        
        // Look for FAQ_1_question, FAQ_1_answer pattern
        while (isset($data["FAQ_{$i}_question"]) && isset($data["FAQ_{$i}_answer"])) {
            $faqs[] = [
                'q' => $data["FAQ_{$i}_question"],
                'a' => $data["FAQ_{$i}_answer"]
            ];
            
            // Remove the individual FAQ items
            unset($data["FAQ_{$i}_question"]);
            unset($data["FAQ_{$i}_answer"]);
            
            $i++;
        }
        
        if (!empty($faqs)) {
            $data['FAQ'] = $faqs;
        }
        
        return $data;
    }
    
    /**
     * Convert Benefit format from AI to individual placeholders.
     */
    private function convertBenefitFormat(array $data): array
    {
        $i = 1;
        
        // Look for Benefit_1_title, Benefit_1_description pattern
        while (isset($data["Benefit_{$i}_title"]) && isset($data["Benefit_{$i}_description"])) {
            // Keep as individual placeholders for template
            // No conversion needed, just ensure they exist
            $i++;
        }
        
        return $data;
    }
    
    /**
     * Convert Feature format from AI to individual placeholders.
     */
    private function convertFeatureFormat(array $data): array
    {
        $i = 1;
        
        // Look for Feature_1_title, Feature_1_description pattern
        while (isset($data["Feature_{$i}_title"]) && isset($data["Feature_{$i}_description"])) {
            // Keep as individual placeholders for template
            // No conversion needed, just ensure they exist
            $i++;
        }
        
        return $data;
    }
    
    /**
     * Validate that required placeholders exist in data.
     */
    public function validateRequiredPlaceholders(array $data): array
    {
        $errors = [];
        $required = [
            'Yoast_title',
            'Yoast_description', 
            'Hero_headline',
            'Hero_description',
            'Business_name',
            'Contact'
        ];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[] = "Missing required field: {$field}";
            }
        }
        
        return $errors;
    }
    
    /**
     * Add default values for missing placeholders.
     */
    public function addDefaultValues(array $data, array $defaults = []): array
    {
        $defaultValues = array_merge([
            'Trust_badge' => 'Dipercaya oleh 500+ Pelanggan',
            'Client_count' => '500+',
            'Client_count_label' => 'Klien Puas',
            'Support_hours' => '24/7',
            'Support_label' => 'Dukungan Teknis',
            'Success_rate' => '100%',
            'Success_label' => 'Responsif',
            'Delivery_time' => '3-7 Hari',
            'Delivery_label' => 'Waktu Pengerjaan',
            'Social_proof_title' => 'Telah Dipercaya oleh Berbagai Bisnis',
            'Social_proof_description' => 'Kami bangga telah menjadi bagian dari perjalanan digital mereka.',
            'Intro_title' => 'Professional Solutions For Your Business',
            'Intro_description' => 'In today\'s digital era, having a strong online presence is crucial for business success.',
            'Pricing_title' => 'Our Service Packages',
            'Pricing_description' => 'Choose the package that best fits your business needs.',
            'Cta_title' => 'Ready to Get Started?',
            'Cta_description' => 'Contact us today for a free consultation.',
            'Cta_button_text' => 'Contact Us Now'
        ], $defaults);
        
        foreach ($defaultValues as $key => $value) {
            if (!isset($data[$key]) || empty($data[$key])) {
                $data[$key] = $value;
            }
        }
        
        return $data;
    }
}
