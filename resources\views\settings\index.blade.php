@extends('layouts.app')

@section('title', 'Pengaturan')
@section('page-title', 'Pengaturan')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-xl font-semibold text-white">Pengaturan</h2>
            <p class="text-gray-400 mt-1">Kelola pengaturan aplikasi Anda</p>
        </div>
    </div>

    <!-- Flash Messages -->
    @if(session('success'))
        <div class="bg-green-800 border border-green-700 text-green-100 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ session('success') }}</span>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-800 border border-red-700 text-red-100 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    <!-- Settings Card -->
    <div class="bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-700">
            <h3 class="text-lg font-medium text-white">Pengaturan Zona Waktu</h3>
            <p class="text-gray-400 text-sm mt-1">Atur zona waktu lokal Anda untuk menampilkan waktu yang akurat</p>
        </div>
        <div class="p-6">

            <form method="POST" action="{{ route('settings.update') }}" class="space-y-6">
                @csrf
                @method('PUT')

                <div>
                    <label for="timezone" class="block text-sm font-medium text-white mb-2">
                        Zona Waktu Lokal
                    </label>
                    <select name="timezone" id="timezone"
                            class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm">
                        @foreach($timezones as $key => $label)
                            <option value="{{ $key }}" {{ ($userSettings->timezone ?? 'UTC') === $key ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                    @error('timezone')
                        <p class="mt-2 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div class="bg-gray-750 p-4 rounded-lg">
                    <p class="text-sm text-gray-300">
                        <strong class="text-white">Waktu saat ini:</strong>
                        <span id="current-time" class="text-cyan-400 font-mono">
                            {{ now()->setTimezone($userSettings->timezone ?? 'UTC')->format('l, d F Y H:i:s T') }}
                        </span>
                    </p>
                    <p class="text-xs text-gray-400 mt-1">
                        Waktu akan otomatis terupdate berdasarkan zona waktu yang dipilih
                    </p>
                </div>

                <div class="flex items-center justify-between pt-4 border-t border-gray-700">
                    <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Simpan Pengaturan
                    </button>

                    <div class="text-sm text-gray-400">
                        Terakhir diupdate: <span class="text-gray-300">{{ $userSettings->updated_at ?? 'Belum pernah' }}</span>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Update current time every second
function updateCurrentTime() {
    const timezone = document.getElementById('timezone').value;
    const now = new Date();

    // Create a date formatter for the selected timezone
    const timeString = now.toLocaleString('id-ID', {
        timeZone: timezone,
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZoneName: 'short'
    });

    document.getElementById('current-time').textContent = timeString;
}

// Update time when timezone selection changes
document.getElementById('timezone').addEventListener('change', updateCurrentTime);

// Update time every second
setInterval(updateCurrentTime, 1000);

// Initial update
updateCurrentTime();
</script>
@endsection