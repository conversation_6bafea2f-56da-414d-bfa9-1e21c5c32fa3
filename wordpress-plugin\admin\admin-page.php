<?php
/**
 * Admin page for Page Generator Integration
 */

if (!defined('ABSPATH')) {
    exit;
}

class PageGenerator_Admin_Page {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('wp_ajax_pg_test_connection', array($this, 'test_connection'));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'Page Generator',
            'Page Generator',
            'manage_options',
            'page-generator',
            array($this, 'admin_page'),
            'dashicons-layout',
            30
        );
        
        add_submenu_page(
            'page-generator',
            'Custom Sections',
            'Custom Sections',
            'manage_options',
            'page-generator-sections',
            array($this, 'sections_page')
        );
        
        add_submenu_page(
            'page-generator',
            'Settings',
            'Settings',
            'manage_options',
            'page-generator-settings',
            array($this, 'settings_page')
        );
    }
    
    public function enqueue_admin_assets($hook) {
        if (strpos($hook, 'page-generator') !== false) {
            wp_enqueue_style(
                'page-generator-admin',
                plugin_dir_url(__FILE__) . '../assets/css/admin-style.css',
                array(),
                '1.0.0'
            );
            
            wp_enqueue_script(
                'page-generator-admin',
                plugin_dir_url(__FILE__) . '../assets/js/admin-script.js',
                array('jquery'),
                '1.0.0',
                true
            );
            
            wp_localize_script('page-generator-admin', 'pgAdmin', array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('page_generator_nonce')
            ));
        }
    }
    
    public function admin_page() {
        ?>
        <div class="pg-admin-container">
            <div class="pg-header">
                <h1>Page Generator Integration</h1>
                <p>Manage your Laravel to WordPress page exports and custom sections</p>
            </div>
            
            <div class="pg-nav-tabs">
                <button class="pg-nav-tab active" data-tab="overview">Overview</button>
                <button class="pg-nav-tab" data-tab="pages">Landing Pages</button>
                <button class="pg-nav-tab" data-tab="connection">Connection</button>
            </div>
            
            <div id="overview-tab" class="pg-tab-content">
                <div class="pg-card">
                    <div class="pg-card-header">
                        <h2>System Status</h2>
                    </div>
                    <div class="pg-card-body">
                        <div class="pg-form-row">
                            <div class="pg-form-col">
                                <h3>Laravel Connection</h3>
                                <div class="pg-badge pg-badge-success">Connected</div>
                                <p>Last sync: <?php echo date('Y-m-d H:i:s'); ?></p>
                            </div>
                            <div class="pg-form-col">
                                <h3>Landing Pages</h3>
                                <div class="pg-text-center">
                                    <div style="font-size: 2rem; font-weight: bold; color: var(--pg-primary);">
                                        <?php echo wp_count_posts('landing_page')->publish; ?>
                                    </div>
                                    <div>Total Pages</div>
                                </div>
                            </div>
                            <div class="pg-form-col">
                                <h3>Custom Sections</h3>
                                <div class="pg-text-center">
                                    <div style="font-size: 2rem; font-weight: bold; color: var(--pg-success);">
                                        <?php 
                                        global $wpdb;
                                        $table_name = $wpdb->prefix . 'page_generator_sections';
                                        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name} WHERE status = 'active'");
                                        echo $count ?: 0;
                                        ?>
                                    </div>
                                    <div>Active Sections</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="pg-card">
                    <div class="pg-card-header">
                        <h2>Recent Activity</h2>
                    </div>
                    <div class="pg-card-body">
                        <p>Recent page exports and system activities will appear here.</p>
                    </div>
                </div>
            </div>
            
            <div id="pages-tab" class="pg-tab-content pg-hidden">
                <div class="pg-card">
                    <div class="pg-card-header">
                        <h2>Landing Pages</h2>
                    </div>
                    <div class="pg-card-body">
                        <?php $this->render_pages_table(); ?>
                    </div>
                </div>
            </div>
            
            <div id="connection-tab" class="pg-tab-content pg-hidden">
                <div class="pg-card">
                    <div class="pg-card-header">
                        <h2>Laravel Connection Test</h2>
                    </div>
                    <div class="pg-card-body">
                        <button id="test-connection" class="pg-btn pg-btn-primary">Test Connection</button>
                        <div id="connection-result" class="pg-mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function sections_page() {
        ?>
        <div class="pg-admin-container">
            <div class="pg-header">
                <h1>Custom HTML Sections</h1>
                <p>Create and manage reusable HTML sections with shortcodes</p>
            </div>
            
            <div class="pg-card">
                <div class="pg-card-header">
                    <h2>Add New Section</h2>
                </div>
                <div class="pg-card-body">
                    <form id="section-form" class="pg-section-editor" method="post" action=""><?php wp_nonce_field('save_custom_section', 'custom_section_nonce'); ?>
                        <div class="pg-form-row">
                            <div class="pg-form-col">
                                <label for="section-name">Section Name</label>
                                <input type="text" id="section-name" name="name" class="pg-form-input" required>
                            </div>
                            <div class="pg-form-col">
                                <label for="section-shortcode">Shortcode</label>
                                <input type="text" id="section-shortcode" name="shortcode" class="pg-form-input" readonly>
                                <button type="button" id="generate-shortcode" class="pg-btn pg-btn-secondary pg-mt-1">Generate</button>
                            </div>
                        </div>
                        
                        <div class="pg-form-group">
                            <label for="section-description">Description</label>
                            <textarea id="section-description" name="description" class="pg-form-input" rows="2"></textarea>
                        </div>
                        
                        <div class="pg-code-editor">
                            <label for="section-html">HTML Content</label>
                            <textarea id="section-html" name="html_content" rows="10" placeholder="Enter your HTML content here..."></textarea>
                        </div>
                        
                        <div class="pg-code-editor">
                            <label for="section-css">CSS (Optional)</label>
                            <textarea id="section-css" name="css_content" rows="5" placeholder="Enter custom CSS here..."></textarea>
                        </div>
                        
                        <div class="pg-code-editor">
                            <label for="section-js">JavaScript (Optional)</label>
                            <textarea id="section-js" name="js_content" rows="5" placeholder="Enter custom JavaScript here..."></textarea>
                        </div>
                        
                        <div class="pg-btn-group">
                            <button type="submit" class="pg-btn pg-btn-primary">Save Section</button>
                            <button type="reset" class="pg-btn pg-btn-secondary">Reset</button>
                        </div>
                        
                        <div class="pg-shortcode-display pg-hidden">
                            <strong>Your shortcode:</strong> <span id="shortcode-preview"></span>
                            <p>Click to copy to clipboard</p>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="pg-card">
                <div class="pg-card-header">
                    <h2>Existing Sections</h2>
                </div>
                <div class="pg-card-body">
                    <?php $this->render_sections_table(); ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function settings_page() {
        ?>
        <div class="pg-admin-container">
            <div class="pg-header">
                <h1>Settings</h1>
                <p>Configure Page Generator Integration settings</p>
            </div>
            
            <div class="pg-card">
                <div class="pg-card-header">
                    <h2>General Settings</h2>
                </div>
                <div class="pg-card-body">
                    <form method="post" action="options.php">
                        <?php
                        settings_fields('page_generator_settings');
                        do_settings_sections('page_generator_settings');
                        ?>
                        
                        <div class="pg-form-group">
                            <label for="auto_image_generation">Auto Image Generation</label>
                            <input type="checkbox" id="auto_image_generation" name="page_generator_auto_images" value="1" 
                                   <?php checked(get_option('page_generator_auto_images', 1)); ?>>
                            <span>Automatically generate featured images for landing pages</span>
                        </div>
                        
                        <div class="pg-form-group">
                            <label for="seo_optimization">SEO Optimization</label>
                            <input type="checkbox" id="seo_optimization" name="page_generator_seo_optimization" value="1" 
                                   <?php checked(get_option('page_generator_seo_optimization', 1)); ?>>
                            <span>Enable automatic SEO optimization for landing pages</span>
                        </div>
                        
                        <button type="submit" class="pg-btn pg-btn-primary">Save Settings</button>
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function render_pages_table() {
        $pages = get_posts(array(
            'post_type' => 'landing_page',
            'post_status' => 'any',
            'numberposts' => -1
        ));
        
        if (empty($pages)) {
            echo '<p>No landing pages found.</p>';
            return;
        }
        
        echo '<table class="pg-table">';
        echo '<thead><tr><th>Title</th><th>Status</th><th>URL</th><th>Created</th><th>Actions</th></tr></thead>';
        echo '<tbody>';
        
        foreach ($pages as $page) {
            $url = get_permalink($page->ID);
            echo '<tr>';
            echo '<td><strong>' . esc_html($page->post_title) . '</strong></td>';
            echo '<td><span class="pg-badge pg-badge-' . ($page->post_status === 'publish' ? 'success' : 'warning') . '">' . ucfirst($page->post_status) . '</span></td>';
            echo '<td><a href="' . esc_url($url) . '" target="_blank">' . esc_html($url) . '</a></td>';
            echo '<td>' . date('Y-m-d H:i', strtotime($page->post_date)) . '</td>';
            echo '<td><a href="' . get_edit_post_link($page->ID) . '" class="pg-btn pg-btn-secondary">Edit</a></td>';
            echo '</tr>';
        }
        
        echo '</tbody></table>';
    }
    
    private function render_sections_table() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'custom_sections';
        
        $sections = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY created_at DESC");
        
        if (empty($sections)) {
            echo '<p>No custom sections found. Create your first section above.</p>';
            return;
        }
        
        echo '<table class="pg-table">';
        echo '<thead><tr><th>Name</th><th>Shortcode</th><th>Status</th><th>Updated</th><th>Actions</th></tr></thead>';
        echo '<tbody>';
        
        foreach ($sections as $section) {
            echo '<tr>';
            echo '<td><strong>' . esc_html($section->name) . '</strong><br><small>' . esc_html($section->description) . '</small></td>';
            echo '<td><code>[' . esc_html($section->shortcode) . ']</code></td>';
            echo '<td><span class="pg-badge pg-badge-success">Active</span></td>';
            echo '<td>' . date('Y-m-d H:i', strtotime($section->created_at)) . '</td>';
            echo '<td>';
            echo '<button class="pg-btn pg-btn-secondary edit-section" data-id="' . $section->id . '">Edit</button> ';
            echo '<button class="pg-btn pg-btn-danger delete-section" data-id="' . $section->id . '">Delete</button>';
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody></table>';
    }
    
    public function test_connection() {
        check_ajax_referer('page_generator_nonce', 'nonce');
        
        // Simple connection test
        $response = wp_remote_get('http://page-gent.test/api/test');
        
        if (is_wp_error($response)) {
            wp_send_json_error('Connection failed: ' . $response->get_error_message());
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if ($data && isset($data['status']) && $data['status'] === 'ok') {
            wp_send_json_success('Connection successful!');
        } else {
            wp_send_json_error('Invalid response from Laravel application');
        }
    }
}

new PageGenerator_Admin_Page();
