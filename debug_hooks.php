<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

// Check all wp_head hooks
global $wp_filter;

echo "=== ALL WP_HEAD HOOKS ===" . PHP_EOL;

if (isset($wp_filter['wp_head'])) {
    foreach ($wp_filter['wp_head']->callbacks as $priority => $callbacks) {
        echo "Priority $priority:" . PHP_EOL;
        foreach ($callbacks as $callback) {
            $function_name = '';
            if (is_array($callback['function'])) {
                if (is_object($callback['function'][0])) {
                    $function_name = get_class($callback['function'][0]) . '::' . $callback['function'][1];
                } else {
                    $function_name = $callback['function'][0] . '::' . $callback['function'][1];
                }
            } else {
                $function_name = $callback['function'];
            }
            echo "  - $function_name" . PHP_EOL;
        }
    }
} else {
    echo "No wp_head hooks found" . PHP_EOL;
}

echo PHP_EOL . "=== CHECKING FOR PAGE GENERATOR HOOKS ===" . PHP_EOL;

// Check specifically for PageGeneratorIntegration hooks
if (isset($wp_filter['wp_head'])) {
    foreach ($wp_filter['wp_head']->callbacks as $priority => $callbacks) {
        foreach ($callbacks as $callback) {
            if (is_array($callback['function']) && is_object($callback['function'][0])) {
                $class_name = get_class($callback['function'][0]);
                if (strpos($class_name, 'PageGenerator') !== false) {
                    echo "Found PageGenerator hook at priority $priority: $class_name::" . $callback['function'][1] . PHP_EOL;
                }
            }
        }
    }
}
