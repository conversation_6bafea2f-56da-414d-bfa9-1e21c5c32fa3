# work-flow.md

This document explains the workflow that must be followed when developing, fixing, and testing code. Each step ensures that tasks are done in a structured, reliable, and professional way.

## Steps

1. **Confirm the user’s request**  
   - Create an outline of what the user wants.  
   - Prepare a solution plan as a clear, actionable to-do list.  

2. **Study and understand the context**  
   - Read the relevant parts of the codebase before making changes.  
   - Understand each requirement in depth.  

3. **Execute the work (code / fixes)**  
   - Work step by step without skipping.  
   - Use existing packages/libraries for new features.  
   - Follow the established CSS theme styles and layout patterns.  
   - Apply MCP tools for design, testing, and debugging throughout development.  

4. **Testing & validation**  
   - Run full tests using <PERSON><PERSON> Playwright (desktop & mobile).  
   - Read logs thoroughly to identify real error data.  
   - Fix issues based on the actual data and document the fix.  
   - Confirm that the root cause is solved.  
   - Make sure all tests pass before moving forward.  

5. **Finalization**  
   - Verify all user requests have been fulfilled.  
   - Take screenshots to visually confirm the result matches the request.  
   - Deliver documentation of fixes and testing.  
   - Only then, mark the task as “done”.
