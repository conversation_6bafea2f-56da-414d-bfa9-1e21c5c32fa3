@extends('layouts.app')

@section('title', 'SEO Analysis - ' . $page->title)
@section('page-title', 'SEO Analysis')

@section('content')
<div class="space-y-6">
    <!-- Page Info -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium text-white">{{ $page->title }}</h3>
                    <p class="text-sm text-gray-400">
                        Campaign: {{ $page->campaign->name }} • Slug: {{ $page->slug }}
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                        @if($seoScore >= 80) bg-green-800 text-green-100
                        @elseif($seoScore >= 60) bg-yellow-800 text-yellow-100
                        @else bg-red-800 text-red-100 @endif">
                        SEO Score: {{ $seoScore }}/100
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Overview -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Title Analysis -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        @if($analysis['title']['score'] >= 80)
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        @elseif($analysis['title']['score'] >= 60)
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        @else
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </div>
                        @endif
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-400 truncate">Title Tag</dt>
                            <dd class="text-lg font-medium text-white">{{ $analysis['title']['score'] }}/100</dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm text-gray-300">
                        Length: {{ $analysis['title']['length'] }} characters
                    </div>
                    @if(!empty($analysis['title']['issues']))
                        <ul class="mt-2 text-xs text-red-400">
                            @foreach($analysis['title']['issues'] as $issue)
                                <li>• {{ $issue }}</li>
                            @endforeach
                        </ul>
                    @endif
                </div>
            </div>
        </div>

        <!-- Description Analysis -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        @if($analysis['description']['score'] >= 80)
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        @elseif($analysis['description']['score'] >= 60)
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        @else
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </div>
                        @endif
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-400 truncate">Meta Description</dt>
                            <dd class="text-lg font-medium text-white">{{ $analysis['description']['score'] }}/100</dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm text-gray-300">
                        Length: {{ $analysis['description']['length'] }} characters
                    </div>
                    @if(!empty($analysis['description']['issues']))
                        <ul class="mt-2 text-xs text-red-400">
                            @foreach($analysis['description']['issues'] as $issue)
                                <li>• {{ $issue }}</li>
                            @endforeach
                        </ul>
                    @endif
                </div>
            </div>
        </div>

        <!-- Schema Analysis -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        @if($analysis['schema']['score'] >= 80)
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        @elseif($analysis['schema']['score'] >= 60)
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                        @else
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </div>
                        @endif
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-400 truncate">Schema Markup</dt>
                            <dd class="text-lg font-medium text-white">{{ $analysis['schema']['score'] }}/100</dd>
                        </dl>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="text-sm text-gray-300">
                        {{ $analysis['schema']['count'] }} schema types found
                    </div>
                    @if(!empty($analysis['schema']['types']))
                        <div class="mt-2 flex flex-wrap gap-1">
                            @foreach($analysis['schema']['types'] as $type)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-cyan-800 text-cyan-100">
                                    {{ $type }}
                                </span>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analysis -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-white mb-4">SEO Recommendations</h3>
            
            @if(!empty($recommendations))
                <div class="space-y-4">
                    @foreach($recommendations as $recommendation)
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                @if($recommendation['priority'] === 'high')
                                    <div class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-bold text-white">!</span>
                                    </div>
                                @elseif($recommendation['priority'] === 'medium')
                                    <div class="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-bold text-white">!</span>
                                    </div>
                                @else
                                    <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-bold text-white">i</span>
                                    </div>
                                @endif
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-white">{{ $recommendation['title'] }}</h4>
                                <p class="text-sm text-gray-400 mt-1">{{ $recommendation['description'] }}</p>
                                @if(isset($recommendation['action']))
                                    <p class="text-xs text-cyan-400 mt-1">{{ $recommendation['action'] }}</p>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-6">
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-white mb-2">Great SEO!</h3>
                    <p class="text-gray-400">This page follows SEO best practices.</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Current SEO Data -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-white mb-4">Current SEO Data</h3>
            
            <div class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-400">Title</dt>
                    <dd class="mt-1 text-sm text-white">{{ $page->seo_data['title'] ?? 'Not set' }}</dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-400">Description</dt>
                    <dd class="mt-1 text-sm text-white">{{ $page->seo_data['description'] ?? 'Not set' }}</dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-400">Focus Keyword</dt>
                    <dd class="mt-1 text-sm text-white">{{ $page->seo_data['focus_keyword'] ?? 'Not set' }}</dd>
                </div>
                
                @if(isset($page->seo_data['schema']) && !empty($page->seo_data['schema']))
                    <div>
                        <dt class="text-sm font-medium text-gray-400">Schema Types</dt>
                        <dd class="mt-1">
                            <div class="flex flex-wrap gap-2">
                                @foreach($page->seo_data['schema'] as $schema)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-800 text-cyan-100">
                                        {{ $schema['@type'] ?? 'Unknown' }}
                                    </span>
                                @endforeach
                            </div>
                        </dd>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-between">
        <a href="{{ route('content-generator.preview', $page) }}" 
           class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Preview
        </a>
        
        <div class="flex space-x-3">
            <button onclick="regenerateSeo()" 
                    class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Regenerate SEO
            </button>
            
            @if($page->status === 'generated')
                <a href="{{ route('export.create', $page->campaign) }}" 
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Export to WordPress
                </a>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
function regenerateSeo() {
    if (confirm('Are you sure you want to regenerate SEO data? This will overwrite current SEO settings.')) {
        fetch(`/seo/{{ $page->id }}/regenerate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error regenerating SEO: ' + data.error);
            }
        })
        .catch(error => {
            alert('Network error: ' + error.message);
        });
    }
}
</script>
@endpush
@endsection
