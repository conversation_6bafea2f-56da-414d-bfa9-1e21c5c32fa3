@extends('layouts.app')

@section('title', 'Export to WordPress - ' . $campaign->name)
@section('page-title', 'Export to WordPress')

@section('content')
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Campaign Info -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-cyan-500 rounded-lg flex items-center justify-center mr-4">
                        <span class="text-sm font-medium text-white">{{ substr($campaign->name, 0, 2) }}</span>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-white">{{ $campaign->name }}</h3>
                        <p class="text-sm text-gray-400">{{ $campaign->business_name }} • {{ $generatedPages->count() }} pages ready</p>
                    </div>
                </div>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-800 text-green-100">
                    {{ $generatedPages->count() }} Pages Ready
                </span>
            </div>
        </div>
    </div>

    <!-- Export Form -->
    <form id="exportForm" action="{{ route('export.store', $campaign) }}" method="POST" class="space-y-6">
        @csrf
        
        <!-- WordPress Connection -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">WordPress Connection</h3>

                @if($campaign->wordpressConnection)
                    <!-- Campaign has a WordPress connection -->
                    <div class="bg-green-900 bg-opacity-50 border border-green-700 rounded-lg p-4 mb-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="text-green-100 font-medium">Using Campaign's WordPress Connection</p>
                                <p class="text-green-200 text-sm">{{ $campaign->wordpressConnection->name }} - {{ $campaign->wordpressConnection->url }}</p>
                            </div>
                        </div>
                    </div>
                @else
                    <!-- No connection set, show manual form -->
                    <div class="bg-yellow-900 bg-opacity-50 border border-yellow-700 rounded-lg p-4 mb-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div>
                                <p class="text-yellow-100 font-medium">No WordPress Connection Set</p>
                                <p class="text-yellow-200 text-sm">Please enter connection details manually or <a href="{{ route('campaigns.edit', $campaign) }}" class="text-yellow-300 hover:text-yellow-100 underline">edit campaign</a> to set a connection.</p>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div class="sm:col-span-2">
                            <label for="wordpress_url" class="block text-sm font-medium text-gray-300">WordPress Site URL</label>
                            <input type="url" name="wordpress_url" id="wordpress_url" required
                                   class="mt-1 form-input w-full"
                                   placeholder="https://yoursite.com"
                                   value="{{ old('wordpress_url') }}">
                            @error('wordpress_url')
                                <p class="mt-2 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-300">Username</label>
                            <input type="text" name="username" id="username" required
                                   class="mt-1 form-input w-full"
                                   value="{{ old('username') }}">
                            @error('username')
                                <p class="mt-2 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-300">Application Password</label>
                            <input type="password" name="password" id="password" required
                                   class="mt-1 form-input w-full">
                            @error('password')
                                <p class="mt-2 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-xs text-gray-400">Use WordPress Application Password, not regular password</p>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button type="button" onclick="testConnection()"
                                class="inline-flex items-center px-3 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Test Connection
                        </button>
                        <div id="connectionStatus" class="mt-2 hidden"></div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Export Settings -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">Export Settings</h3>

                <div class="space-y-4">
                    <!-- Export Type Selection -->
                    <div>
                        <label for="export_type" class="block text-sm font-medium text-gray-300">Export as</label>
                        <select name="export_type" id="export_type" required class="mt-1 form-input w-full" onchange="toggleExportOptions()">
                            <option value="page" {{ old('export_type', 'page') === 'page' ? 'selected' : '' }}>WordPress Page</option>
                            <option value="post" {{ old('export_type') === 'post' ? 'selected' : '' }}>WordPress Post</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-400">Choose whether to export as WordPress Pages or Posts</p>
                        @error('export_type')
                            <p class="mt-2 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status Selection -->
                    <div>
                        <label for="post_status" class="block text-sm font-medium text-gray-300">Status</label>
                        <select name="post_status" id="post_status" required class="mt-1 form-input w-full">
                            <option value="publish" {{ old('post_status', 'publish') === 'publish' ? 'selected' : '' }}>Published</option>
                            <option value="draft" {{ old('post_status') === 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="private" {{ old('post_status') === 'private' ? 'selected' : '' }}>Private</option>
                        </select>
                        @error('post_status')
                            <p class="mt-2 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Category Selection (only for Posts) -->
                    <div id="categorySection" style="display: none;">
                        <label for="category_id" class="block text-sm font-medium text-gray-300">Category (Optional)</label>
                        <select name="category_id" id="category_id" class="mt-1 form-input w-full">
                            <option value="">Select Category</option>
                        </select>
                        <button type="button" onclick="loadCategories()"
                                class="mt-2 text-sm text-cyan-400 hover:text-cyan-300">
                            Load Categories from WordPress
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pages Preview -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg leading-6 font-medium text-white">Pages to Export ({{ $generatedPages->count() }})</h3>
                    <div class="flex items-center space-x-2">
                        <button type="button" onclick="selectAllPages()" class="text-xs text-cyan-400 hover:text-cyan-300">Select All</button>
                        <span class="text-gray-500">|</span>
                        <button type="button" onclick="deselectAllPages()" class="text-xs text-cyan-400 hover:text-cyan-300">Deselect All</button>
                    </div>
                </div>

                @if(isset($preSelectedPageId))
                    <div class="mb-4 p-3 bg-blue-900 bg-opacity-50 border border-blue-700 rounded-lg">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="text-blue-100 text-sm">Page pre-selected for export. You can select additional pages if needed.</p>
                        </div>
                    </div>
                @endif

                <div class="max-h-64 overflow-y-auto">
                    <div class="space-y-2">
                        @foreach($generatedPages as $page)
                            <div class="flex items-center justify-between p-3 bg-gray-700 rounded-md">
                                <div class="flex items-center">
                                    <input type="checkbox"
                                           name="page_ids[]"
                                           value="{{ $page->id }}"
                                           class="page-checkbox mr-3 rounded border-gray-600 text-cyan-600 focus:ring-cyan-500 focus:ring-offset-gray-800"
                                           {{ (isset($preSelectedPageId) && $preSelectedPageId == $page->id) ? 'checked' : '' }}>
                                    <div class="w-8 h-8 bg-cyan-500 rounded-md flex items-center justify-center mr-3">
                                        <span class="text-xs font-medium text-white">{{ $loop->iteration }}</span>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-white">
                                            {{ $page->page_data['Hero_headline'] ?? $page->title }}
                                            @if(isset($preSelectedPageId) && $preSelectedPageId == $page->id)
                                                <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-800 text-blue-100">
                                                    Pre-selected
                                                </span>
                                            @endif
                                        </h4>
                                        <p class="text-xs text-gray-400">{{ $page->slug }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($page->seo_data && isset($page->seo_data['title']))
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-800 text-green-100">
                                            SEO Ready
                                        </span>
                                    @endif
                                    <a href="{{ route('content-generator.preview', $page) }}"
                                       class="text-cyan-400 hover:text-cyan-300 text-xs">
                                        Preview
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="{{ route('campaigns.show', $campaign) }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                Cancel
            </a>
            <button type="submit" id="exportButton"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                Start Export
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
let connectionTested = false;

function toggleExportOptions() {
    const exportType = document.getElementById('export_type').value;
    const categorySection = document.getElementById('categorySection');

    if (exportType === 'post') {
        categorySection.style.display = 'block';
    } else {
        categorySection.style.display = 'none';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleExportOptions();
});

function testConnection() {
    const url = document.getElementById('wordpress_url').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    if (!url || !username || !password) {
        showConnectionStatus('error', 'Please fill in all connection fields');
        return;
    }
    
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Testing...';
    button.disabled = true;
    
    fetch('{{ route("export.test-connection") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            wordpress_url: url,
            username: username,
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showConnectionStatus('success', 'Connection successful!');
            connectionTested = true;
        } else {
            showConnectionStatus('error', data.error || 'Connection failed');
            connectionTested = false;
        }
    })
    .catch(error => {
        showConnectionStatus('error', 'Network error: ' + error.message);
        connectionTested = false;
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function loadCategories() {
    const url = document.getElementById('wordpress_url').value;
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    if (!url || !username || !password) {
        alert('Please fill in WordPress connection details first');
        return;
    }
    
    fetch('{{ route("export.get-categories") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            wordpress_url: url,
            username: username,
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const select = document.getElementById('category_id');
            select.innerHTML = '<option value="">Select Category</option>';
            
            data.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                select.appendChild(option);
            });
        } else {
            alert('Failed to load categories: ' + data.error);
        }
    })
    .catch(error => {
        alert('Network error: ' + error.message);
    });
}

function showConnectionStatus(type, message) {
    const statusDiv = document.getElementById('connectionStatus');
    statusDiv.className = `mt-2 p-2 rounded-md text-sm ${type === 'success' ? 'bg-green-800 text-green-100' : 'bg-red-800 text-red-100'}`;
    statusDiv.textContent = message;
    statusDiv.classList.remove('hidden');
}

// Form submission validation (temporarily disabled for testing)
document.getElementById('exportForm').addEventListener('submit', function(e) {
    // if (!connectionTested) {
    //     e.preventDefault();
    //     alert('Please test the WordPress connection first');
    //     return;
    // }
    
    // Check if at least one page is selected
    const checkedBoxes = document.querySelectorAll('.page-checkbox:checked');

    if (checkedBoxes.length === 0) {
        e.preventDefault();
        alert('Please select at least one page to export.');
        return false;
    }

    const button = document.getElementById('exportButton');
    button.innerHTML = '<svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Starting Export...';
    button.disabled = true;
});

function selectAllPages() {
    const checkboxes = document.querySelectorAll('.page-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

function deselectAllPages() {
    const checkboxes = document.querySelectorAll('.page-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}
</script>
@endpush
@endsection
