<?php

namespace App\Http\Controllers;

use App\Models\WordPressConnection;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class WordPressConnectionController extends Controller
{
    /**
     * Display a listing of WordPress connections.
     */
    public function index(): View
    {
        $connections = WordPressConnection::orderBy('is_default', 'desc')
            ->orderBy('name')
            ->get();

        return view('wordpress.connections.index', compact('connections'));
    }

    /**
     * Show the form for creating a new WordPress connection.
     */
    public function create(): View
    {
        return view('wordpress.connections.create');
    }

    /**
     * Store a newly created WordPress connection.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:word_press_connections,name',
            'url' => 'required|url|max:255',
            'username' => 'required|string|max:255',
            'password' => 'required|string|max:255',
            'is_default' => 'boolean'
        ]);

        $connection = WordPressConnection::create($validated);

        // Set as default if requested and no other default exists
        if ($request->boolean('is_default') || !WordPressConnection::where('is_default', true)->exists()) {
            $connection->setAsDefault();
        }

        // Test connection immediately
        $testResult = $connection->testConnection();

        if ($testResult['success']) {
            return redirect()->route('wordpress.connections.index')
                ->with('success', 'WordPress connection created and tested successfully!');
        } else {
            return redirect()->route('wordpress.connections.index')
                ->with('warning', 'WordPress connection created but test failed: ' . $testResult['error']);
        }
    }

    /**
     * Display the specified WordPress connection.
     */
    public function show(WordPressConnection $connection): View
    {
        return view('wordpress.connections.show', compact('connection'));
    }

    /**
     * Show the form for editing the specified WordPress connection.
     */
    public function edit(WordPressConnection $connection): View
    {
        return view('wordpress.connections.edit', compact('connection'));
    }

    /**
     * Update the specified WordPress connection.
     */
    public function update(Request $request, WordPressConnection $connection): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:word_press_connections,name,' . $connection->id,
            'url' => 'required|url|max:255',
            'username' => 'required|string|max:255',
            'password' => 'nullable|string|max:255',
            'is_default' => 'boolean',
            'is_active' => 'boolean'
        ]);

        // Only update password if provided
        if (empty($validated['password'])) {
            unset($validated['password']);
        }

        $connection->update($validated);

        // Handle default setting
        if ($request->boolean('is_default')) {
            $connection->setAsDefault();
        }

        return redirect()->route('wordpress.connections.index')
            ->with('success', 'WordPress connection updated successfully!');
    }

    /**
     * Remove the specified WordPress connection.
     */
    public function destroy(WordPressConnection $connection): RedirectResponse
    {
        // Prevent deletion of default connection if it's the only one
        if ($connection->is_default && WordPressConnection::active()->count() <= 1) {
            return redirect()->route('wordpress.connections.index')
                ->with('error', 'Cannot delete the only active connection.');
        }

        $connection->delete();

        return redirect()->route('wordpress.connections.index')
            ->with('success', 'WordPress connection deleted successfully!');
    }

    /**
     * Test WordPress connection via AJAX.
     */
    public function testConnection(WordPressConnection $connection): JsonResponse
    {
        $result = $connection->testConnection();
        return response()->json($result);
    }

    /**
     * Test new WordPress connection before saving.
     */
    public function testNewConnection(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'wordpress_url' => 'required|url',
            'username' => 'required|string',
            'password' => 'required|string'
        ]);

        $config = [
            'wordpress_url' => $validated['wordpress_url'],
            'username' => $validated['username'],
            'password' => $validated['password']
        ];

        $exportService = app(\App\Services\WordPressExportService::class);
        $result = $exportService->testConnection($config);

        return response()->json($result);
    }

    /**
     * Set connection as default via AJAX.
     */
    public function setDefault(WordPressConnection $connection): JsonResponse
    {
        $connection->setAsDefault();

        return response()->json([
            'success' => true,
            'message' => 'Connection set as default successfully!'
        ]);
    }

    /**
     * Toggle connection active status via AJAX.
     */
    public function toggleActive(WordPressConnection $connection): JsonResponse
    {
        $connection->update(['is_active' => !$connection->is_active]);

        return response()->json([
            'success' => true,
            'is_active' => $connection->is_active,
            'message' => 'Connection status updated successfully!'
        ]);
    }
}
