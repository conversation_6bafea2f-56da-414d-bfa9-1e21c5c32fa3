<?php

namespace App\Services;

use App\Models\GeneratedPage;
use App\Models\Template;
use App\Models\WordPressConnection;
use App\Services\WordPressContentService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class WordPressIntegrationService
{
    protected WordPressContentService $contentService;

    public function __construct(WordPressContentService $contentService)
    {
        $this->contentService = $contentService;
    }

    /**
     * Export a generated page to WordPress
     */
    public function exportPage(GeneratedPage $page): array
    {
        Log::info('Starting page export', [
            'page_id' => $page->id,
            'page_title' => $page->title
        ]);

        $connection = $page->campaign->wordPressConnection;

        if (!$connection) {
            Log::error('No WordPress connection found', [
                'page_id' => $page->id,
                'campaign_id' => $page->campaign->id
            ]);

            return [
                'success' => false,
                'message' => 'Export failed: No WordPress connection is associated with this campaign.'
            ];
        }

        try {
            $template = $page->template;

            Log::info('Template loaded', [
                'template_id' => $template->id,
                'template_name' => $template->name,
                'has_head_main_separation' => $template->hasHeadMainSeparation()
            ]);

            // Check if template uses head/main separation
            if ($template->hasHeadMainSeparation()) {
                return $this->exportHeadMainPage($page, $connection);
            }

            // Legacy export for old templates
            return $this->exportLegacyPage($page, $connection);

        } catch (Exception $e) {
            Log::error('WordPress export failed', [
                'page_id' => $page->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Export page to WordPress with clean separation of content and assets.
     */
    private function exportHeadMainPage(GeneratedPage $page, WordPressConnection $connection): array
    {
        Log::info('Exporting page to WordPress with clean separation', [
            'page_id' => $page->id,
            'connection_url' => $connection->url
        ]);

        $template = $page->template;

        // Send static sections to WordPress plugin
        $this->sendStaticSectionsToWordPress($page, $connection);

        // Process complete HTML content with data
        $processedHeadContent = $this->processContent($template->head_content ?? '', $page->page_data);
        $processedMainContent = $this->processContent($template->main_content ?? $template->html_content, $page->page_data);

        // NEW APPROACH: Separate HTML content from assets
        $separatedContent = $this->separateContentFromAssets($processedHeadContent, $processedMainContent);

        Log::info('Separated content and assets', [
            'html_content_length' => strlen($separatedContent['html_content']),
            'assets_content_length' => strlen($separatedContent['assets_content'])
        ]);

        // Generate SEO data
        $seoTitle = $page->page_data['Yoast_title'] ?? '';
        $seoDescription = $page->page_data['Yoast_description'] ?? '';
        $seoKeyword = $page->page_data['Yoast_focus_keyword'] ?? '';

        // If SEO data is empty, generate from page content
        if (empty($seoTitle)) {
            $seoTitle = ($page->page_data['Hero_headline'] ?? $page->title) . ' - ' . ($page->campaign->business_name ?? 'Website');
        }

        if (empty($seoDescription)) {
            $seoDescription = $page->page_data['Hero_description'] ?? 'Professional website services with modern design and SEO optimization.';
            $seoDescription = substr($seoDescription, 0, 160);
        }

        if (empty($seoKeyword)) {
            $seoKeyword = $page->campaign->main_keyword ?? 'website professional';
        }

        $pageData = [
            'title' => $page->page_data['Hero_headline'] ?? $page->title,
            'html_content' => $separatedContent['html_content'], // Goes to post_content
            'assets_content' => $separatedContent['assets_content'], // Goes to post meta
            'seo_title' => $seoTitle,
            'seo_description' => $seoDescription,
            'seo_keyword' => $seoKeyword,
            'slug' => $page->slug,
            'template_id' => $template->id,
            'page_id' => $page->id
        ];

        // Send clean separated data to WordPress
        $response = $this->sendCleanDataToWordPress($pageData, $connection);

        if ($response['success']) {
            $page->update([
                'status' => 'exported',
                'wordpress_url' => $response['data']['page_url'] ?? null
            ]);

            Log::info('Page exported to WordPress successfully with clean separation', [
                'page_id' => $page->id,
                'wordpress_url' => $response['data']['page_url'] ?? null
            ]);
        }

        return $response;
    }

    /**
     * Separate HTML content from assets (CSS, JS, fonts).
     * HTML content goes to post_content, assets go to post meta.
     */
    private function separateContentFromAssets(string $headContent, string $mainContent): array
    {
        // Extract assets from head content
        $assets = [];
        $cleanMainContent = $mainContent;

        // Extract CSS (style tags and link tags)
        preg_match_all('/<style[^>]*>.*?<\/style>/is', $headContent, $styleMatches);
        preg_match_all('/<link[^>]*(?:rel=["\']stylesheet["\']|href=["\'][^"\']*\.css[^"\']*["\'])[^>]*>/i', $headContent, $linkMatches);

        $assets = array_merge($assets, $styleMatches[0], $linkMatches[0]);

        // Extract JavaScript (script tags)
        preg_match_all('/<script[^>]*>.*?<\/script>/is', $headContent, $scriptMatches);
        preg_match_all('/<script[^>]*src=["\'][^"\']*["\'][^>]*><\/script>/i', $headContent, $scriptSrcMatches);

        $assets = array_merge($assets, $scriptMatches[0], $scriptSrcMatches[0]);

        // Extract fonts (Google Fonts, font-face, etc.)
        preg_match_all('/<link[^>]*(?:href=["\'][^"\']*fonts[^"\']*["\']|rel=["\']preconnect["\'])[^>]*>/i', $headContent, $fontMatches);

        $assets = array_merge($assets, $fontMatches[0]);

        // Extract meta tags for assets (preload, prefetch, etc.)
        preg_match_all('/<meta[^>]*(?:name=["\'](?:theme-color|msapplication-|apple-)[^"\']*["\']|property=["\']og:image["\'])[^>]*>/i', $headContent, $metaMatches);

        $assets = array_merge($assets, $metaMatches[0]);

        // Clean and deduplicate assets
        $assets = array_unique(array_filter($assets));

        return [
            'html_content' => $cleanMainContent,
            'assets_content' => implode("\n", $assets)
        ];
    }

    /**
     * Export legacy page (backward compatibility).
     */
    private function exportLegacyPage(GeneratedPage $page, WordPressConnection $connection): array
    {
        $template = $page->template;
        $staticSections = $this->getStaticSections($template);

        // Send static sections to WordPress plugin
        $this->sendStaticSectionsToWordPress($page, $connection);

        // Prepare main page content
        $pageContent = $this->preparePageContent($page, $staticSections);

        // Generate fallback SEO data if not present
        $seoTitle = $page->page_data['Yoast_title'] ?? '';
        $seoDescription = $page->page_data['Yoast_description'] ?? '';
        $seoKeyword = $page->page_data['Yoast_focus_keyword'] ?? '';

        // If SEO data is empty, generate from page content
        if (empty($seoTitle)) {
            $seoTitle = ($page->page_data['Hero_headline'] ?? $page->title) . ' - ' . ($page->campaign->business_name ?? 'Website');
        }

        if (empty($seoDescription)) {
            $seoDescription = $page->page_data['Hero_description'] ?? 'Professional website services with modern design and SEO optimization.';
            // Limit to 160 characters for SEO
            $seoDescription = substr($seoDescription, 0, 160);
        }

        if (empty($seoKeyword)) {
            $seoKeyword = $page->campaign->main_keyword ?? 'website professional';
        }

        $pageData = [
            'title' => $page->page_data['Hero_headline'] ?? $page->title,
            'content' => $pageContent,
            'seo_title' => $seoTitle,
            'seo_description' => $seoDescription,
            'seo_keyword' => $seoKeyword,
            'slug' => $page->slug,
            'template_id' => $template->id,
            'page_id' => $page->id,
            'uses_head_main_separation' => false
        ];

        // Send main page data to WordPress
        $response = $this->sendToWordPress($pageData, $staticSections, $connection);

        if ($response['success']) {
            $page->update([
                'status' => 'exported',
                'wordpress_url' => $response['data']['page_url'] ?? null
            ]);

            Log::info('Legacy page exported to WordPress successfully', [
                'page_id' => $page->id,
                'wordpress_url' => $response['data']['page_url'] ?? null
            ]);
        }

        return $response;
    }

    /**
     * Export multiple pages to WordPress
     */
    public function exportPages($pages): array
    {
        $results = [];
        $successCount = 0;
        $failCount = 0;

        foreach ($pages as $page) {
            $result = $this->exportPage($page);
            $results[] = $result;
            
            if ($result['success']) {
                $successCount++;
            } else {
                $failCount++;
            }
        }

        return [
            'success' => $failCount === 0,
            'total' => count($pages),
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'results' => $results
        ];
    }

    /**
     * Get static sections for a template
     */
    public function getStaticSections(Template $template): array
    {
        $staticSections = [];
        $sectionSettings = $template->getSectionSettings();

        foreach ($sectionSettings as $sectionName => $settings) {
            if ($settings['type'] === 'static' && !empty($settings['html_content'])) {
                $shortcodeName = "static_{$sectionName}_template_{$template->id}";
                $staticSections[$shortcodeName] = $settings['html_content'];
            }
        }

        return $staticSections;
    }

    /**
     * Prepare page content with shortcodes (WordPress export - body content only)
     */
    public function preparePageContent(GeneratedPage $page, array $staticSections): string
    {
        // For WordPress export, extract only body content (no DOCTYPE, html, head tags)
        return $this->contentService->prepareContentForWordPress($page->html_with_shortcodes);
    }

    /**
     * Send static sections to WordPress plugin
     */
    private function sendStaticSectionsToWordPress(GeneratedPage $page, WordPressConnection $connection): void
    {
        $template = $page->template;
        $sectionSettings = $template->getSectionSettings();

        foreach ($sectionSettings as $sectionName => $settings) {
            if ($settings['enabled'] && $settings['type'] === 'static' && !empty($settings['html_content'])) {
                $this->sendStaticSectionToPlugin($template, $sectionName, $settings['html_content'], $connection);
            }
        }
    }

    /**
     * Send individual static section to WordPress plugin using the new AJAX endpoint
     */
    private function sendStaticSectionToPlugin(Template $template, string $sectionName, string $sectionHtml, WordPressConnection $connection): void
    {
        $ajaxUrl = rtrim($connection->url, '/') . '/wp-admin/admin-ajax.php';

        $shortcode = "static_{$sectionName}_template_{$template->id}";
        $sectionData = [
            'action'    => 'save_static_section_from_laravel', // The new, dedicated AJAX action
            'shortcode' => $shortcode,
            'title'     => ucfirst(str_replace('_', ' ', $sectionName)) . ' Section',
            'content'   => $sectionHtml,
        ];

        try {
            $response = Http::timeout(30)
                ->asForm()
                ->withHeaders(['User-Agent' => 'Laravel Page Generator/1.0'])
                ->post($ajaxUrl, $sectionData);

            if (!$response->successful() || strpos($response->body(), '"success":false') !== false) {
                Log::warning('Failed to send static section to WordPress plugin via AJAX', [
                    'shortcode' => $shortcode,
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'url' => $ajaxUrl
                ]);
            } else {
                Log::info('Successfully sent static section to WordPress via AJAX.', ['shortcode' => $shortcode]);
            }
        } catch (Exception $e) {
            Log::error('Error sending static section to WordPress plugin via AJAX', [
                'shortcode' => $shortcode,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send clean separated data to WordPress via AJAX
     */
    private function sendCleanDataToWordPress(array $pageData, WordPressConnection $connection): array
    {
        $ajaxUrl = rtrim($connection->url, '/') . '/wp-admin/admin-ajax.php';

        $data = [
            'action' => 'receive_laravel_clean_data',
            'page_data' => json_encode($pageData)
        ];

        try {
            Log::info('Sending clean separated data to WordPress', [
                'url' => $ajaxUrl,
                'html_content_length' => strlen($pageData['html_content']),
                'assets_content_length' => strlen($pageData['assets_content'])
            ]);

            $response = Http::timeout(30)
                ->asForm()
                ->withHeaders(['User-Agent' => 'Laravel Page Generator/1.0'])
                ->post($ajaxUrl, $data);

            if ($response->successful()) {
                $responseData = $response->json();

                Log::info('WordPress response received', [
                    'response' => $responseData
                ]);

                if (isset($responseData['success']) && $responseData['success']) {
                    return [
                        'success' => true,
                        'data' => $responseData['data'] ?? [],
                        'message' => 'Page exported successfully with clean separation'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => $responseData['data']['message'] ?? $responseData['data'] ?? 'Unknown WordPress error'
                    ];
                }
            } else {
                Log::error('WordPress HTTP error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);

                return [
                    'success' => false,
                    'message' => 'HTTP Error: ' . $response->status() . ' - ' . $response->body()
                ];
            }

        } catch (Exception $e) {
            Log::error('WordPress Clean Data Integration Error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Connection error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send head/main separated data to WordPress via AJAX (DEPRECATED - kept for backward compatibility)
     */
    private function sendHeadMainToWordPress(array $pageData, array $shortcodeMapping, WordPressConnection $connection): array
    {
        // Redirect to new clean method
        return $this->sendCleanDataToWordPress($pageData, $connection);
    }

    /**
     * Send main page data to WordPress via AJAX (legacy)
     */
    private function sendToWordPress(array $pageData, array $staticSections, WordPressConnection $connection): array
    {
        $ajaxUrl = rtrim($connection->url, '/') . '/wp-admin/admin-ajax.php';

        $data = [
            'action' => 'receive_laravel_data',
            'page_data' => json_encode($pageData),
            'static_sections' => json_encode($staticSections) // This is somewhat redundant now but harmless
        ];

        try {
            $response = Http::timeout(30)
                ->asForm()
                ->withHeaders(['User-Agent' => 'Laravel Page Generator/1.0'])
                ->post($ajaxUrl, $data);

            if ($response->successful()) {
                $responseData = $response->json();

                if (isset($responseData['success']) && $responseData['success']) {
                    return [
                        'success' => true,
                        'data' => $responseData['data'] ?? [],
                        'message' => 'Page exported successfully'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => $responseData['data']['message'] ?? $responseData['data'] ?? 'Unknown WordPress error'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'HTTP Error: ' . $response->status() . ' - ' . $response->body()
                ];
            }

        } catch (Exception $e) {
            Log::error('WordPress Integration Error:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Connection error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Process content by replacing placeholders with data.
     */
    private function processContent(string $content, array $data): string
    {
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $content = str_replace('{{' . $key . '}}', $value, $content);
            }
        }

        return $content;
    }
}