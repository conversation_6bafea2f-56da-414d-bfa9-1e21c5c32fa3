<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

// Create table manually
global $wpdb;
$table_name = $wpdb->prefix . 'custom_sections';

$charset_collate = $wpdb->get_charset_collate();

$sql = "CREATE TABLE $table_name (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    shortcode varchar(100) NOT NULL UNIQUE,
    description text,
    html_content longtext NOT NULL,
    css_content longtext,
    js_content longtext,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY shortcode (shortcode)
) $charset_collate;";

require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
$result = dbDelta($sql);

echo 'Table creation result:' . PHP_EOL;
print_r($result);

// Check if table exists now
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
echo 'Table exists after creation: ' . ($table_exists ? 'YES' : 'NO') . PHP_EOL;

if ($table_exists) {
    // Show table structure
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    echo 'Table structure:' . PHP_EOL;
    foreach ($columns as $column) {
        echo '  - ' . $column->Field . ' (' . $column->Type . ')' . PHP_EOL;
    }
}
