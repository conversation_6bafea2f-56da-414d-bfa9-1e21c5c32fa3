# rule.md

This document contains the basic rules (Rule) and the workflow (Workflow) to follow when developing, fixing, or testing code.

## Rule

- Follow the existing patterns for theme CSS styles and layout templates.  
- Use industry-standard best practices and common professional code structures.  
- Always use existing packages/libraries when adding new features.  
- Use MCP tools that available for design, testing, and debugging.    
- Read logs fully; never assume the cause of an error.  
- Fix issues based on real error data and document each fix.  
- Find the root cause of problems; do not take shortcuts.  
- Test thoroughly using MCP Playwright for both desktop and mobile.  
- Ensure all tests pass before delivery.  
- Before sending to the user, make sure every user request is fulfilled.  
- Never mark a task as “done” without a screenshot that visually verifies the result exactly matches the request — DOM checks alone are not enough.

