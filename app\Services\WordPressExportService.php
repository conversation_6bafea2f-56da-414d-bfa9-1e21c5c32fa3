<?php

namespace App\Services;

use App\Models\Campaign;
use App\Models\GeneratedPage;
use App\Models\ExportLog;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class WordPressExportService
{
    protected WordPressIntegrationService $integrationService;

    public function __construct(WordPressIntegrationService $integrationService)
    {
        $this->integrationService = $integrationService;
    }

    public function exportPages(Campaign $campaign, array $pageIds): ExportLog
    {
        Log::info('Starting exportPages', [
            'campaign_id' => $campaign->id,
            'page_ids' => $pageIds
        ]);

        $pagesToExport = $campaign->generatedPages()->whereIn('id', $pageIds)->get();

        Log::info('Pages to export loaded', [
            'pages_count' => $pagesToExport->count(),
            'pages' => $pagesToExport->pluck('id', 'title')->toArray()
        ]);

        $exportLog = ExportLog::create([
            'campaign_id' => $campaign->id,
            'wordpress_connection_id' => $campaign->wordpress_connection_id,
            'wordpress_site_url' => $campaign->wordpressConnection->url ?? 'N/A',
            'total_pages' => $pagesToExport->count(),
            'status' => 'processing',
            'export_settings' => ['page_ids' => $pageIds], // Store selected page IDs
        ]);

        Log::info('Export log created', [
            'export_log_id' => $exportLog->id
        ]);

        try {
            $this->processExport($exportLog, $pagesToExport);
        } catch (\Exception $e) {
            Log::error('Exception in exportPages', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->handleExportError($exportLog, $e);
        }

        return $exportLog;
    }

    private function processExport(ExportLog $exportLog, $pages): void
    {
        Log::info('Starting export process', [
            'export_log_id' => $exportLog->id,
            'pages_count' => $pages->count()
        ]);

        $exportData = ['pages' => [], 'errors' => []];
        $exportedCount = 0;
        $failedCount = 0;

        foreach ($pages as $page) {
            Log::info('Processing page for export', [
                'page_id' => $page->id,
                'page_title' => $page->title
            ]);

            try {
                // Delegate the entire export logic to the already-fixed integration service.
                $result = $this->integrationService->exportPage($page);

                Log::info('Export result received', [
                    'page_id' => $page->id,
                    'success' => $result['success'],
                    'message' => $result['message'] ?? 'No message'
                ]);

                if ($result['success']) {
                    $exportedCount++;
                    $exportData['pages'][] = [
                        'page_id' => $page->id,
                        'wordpress_id' => $result['data']['page_id'] ?? null,
                        'url' => $result['data']['page_url'] ?? null,
                        'exported_at' => now()->toISOString()
                    ];
                } else {
                    $failedCount++;
                    $exportData['errors'][] = [
                        'page_id' => $page->id,
                        'error' => $result['message'],
                        'failed_at' => now()->toISOString()
                    ];
                }
            } catch (\Exception $e) {
                Log::error('Exception during page export', [
                    'page_id' => $page->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                $failedCount++;
                $exportData['errors'][] = [
                    'page_id' => $page->id,
                    'error' => $e->getMessage(),
                    'failed_at' => now()->toISOString()
                ];
            }

            $exportLog->update([
                'exported_pages' => $exportedCount,
                'failed_pages' => $failedCount,
                'export_data' => $exportData
            ]);
        }

        $exportLog->update([
            'status' => $failedCount > 0 ? 'completed_with_errors' : 'completed',
            'export_data' => $exportData
        ]);
    }

    private function handleExportError(ExportLog $exportLog, \Exception $e): void
    {
        $exportData = $exportLog->export_data ?? [];
        $exportData['error'] = $e->getMessage();
        $exportData['failed_at'] = now()->toISOString();

        $exportLog->update([
            'status' => 'failed',
            'export_data' => $exportData
        ]);

        Log::error('WordPress export failed', [
            'export_log_id' => $exportLog->id,
            'error' => $e->getMessage()
        ]);
    }

    public function getProgress(ExportLog $exportLog): array
    {
        $totalPages = $exportLog->total_pages;
        $exportedPages = $exportLog->exported_pages;
        $failedPages = $exportLog->failed_pages;
        $processedPages = $exportedPages + $failedPages;

        $progress = $totalPages > 0 ? round(($processedPages / $totalPages) * 100) : 0;

        return [
            'status' => $exportLog->status,
            'progress' => $progress,
            'total_pages' => $totalPages,
            'exported_pages' => $exportedPages,
            'failed_pages' => $failedPages,
            'processed_pages' => $processedPages,
            'is_completed' => in_array($exportLog->status, ['completed', 'completed_with_errors', 'failed'])
        ];
    }

    /**
     * Test WordPress connection
     */
    public function testConnection(array $config): array
    {
        try {
            $ajaxUrl = rtrim($config['wordpress_url'], '/') . '/wp-admin/admin-ajax.php';

            $response = Http::timeout(10)
                ->asForm()
                ->withHeaders(['User-Agent' => 'Laravel Page Generator/1.0'])
                ->post($ajaxUrl, [
                    'action' => 'receive_laravel_head_main_data',
                    'page_data' => json_encode([
                        'title' => 'Connection Test',
                        'main_content' => 'Test content',
                        'head_content' => '<title>Test</title>',
                        'slug' => 'connection-test-' . time()
                    ])
                ]);

            if ($response->successful()) {
                $responseData = $response->json();

                if (isset($responseData['success']) && $responseData['success']) {
                    return [
                        'success' => true,
                        'message' => 'Connection successful'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => $responseData['data']['message'] ?? 'Unknown WordPress error'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'HTTP Error: ' . $response->status() . ' - ' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection error: ' . $e->getMessage()
            ];
        }
    }
}