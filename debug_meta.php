<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

$post_id = 930; // ID dari test shortcode landing page

echo 'POST CONTENT (yang user edit):' . PHP_EOL;
$post = get_post($post_id);
echo $post->post_content . PHP_EOL . PHP_EOL;

echo 'META FIELDS (yang seben<PERSON><PERSON> ditampilkan):' . PHP_EOL;
$page_data = get_post_meta($post_id, 'page_data', true);
if ($page_data) {
    echo 'page_data[main_content]: ' . substr($page_data['main_content'], 0, 200) . '...' . PHP_EOL;
    echo 'page_data[head_content]: ' . substr($page_data['head_content'], 0, 200) . '...' . PHP_EOL;
} else {
    echo 'No page_data found' . PHP_EOL;
}

$old_main = get_post_meta($post_id, 'page_generator_main_content', true);
if ($old_main) {
    echo 'page_generator_main_content: ' . substr($old_main, 0, 200) . '...' . PHP_EOL;
}

echo PHP_EOL . 'ALL META KEYS:' . PHP_EOL;
$all_meta = get_post_meta($post_id);
foreach ($all_meta as $key => $value) {
    if (strpos($key, 'page_generator') !== false || $key === 'page_data') {
        echo $key . ': ' . (is_array($value[0]) ? 'ARRAY' : substr($value[0], 0, 100)) . PHP_EOL;
    }
}
?>
