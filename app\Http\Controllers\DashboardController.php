<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Campaign;
use App\Models\Template;
use App\Models\GeneratedPage;
use App\Models\ExportLog;
use Illuminate\View\View;

class DashboardController extends Controller
{
    /**
     * Show the application dashboard.
     */
    public function index(): View
    {
        // Get statistics
        $stats = [
            'total_campaigns' => Campaign::count(),
            'active_campaigns' => Campaign::where('status', 'active')->count(),
            'total_templates' => Template::where('is_active', true)->count(),
            'total_generated_pages' => GeneratedPage::count(),
            'total_exported_pages' => GeneratedPage::where('status', 'exported')->count(),
            'recent_exports' => ExportLog::where('status', 'completed')->count(),
        ];

        // Get recent campaigns
        $recentCampaigns = Campaign::with(['generatedPages'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get recent generated pages
        $recentPages = GeneratedPage::with(['campaign', 'template'])
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get recent export logs
        $recentExports = ExportLog::with('campaign')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get campaign status distribution
        $campaignStatusStats = Campaign::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        return view('dashboard', compact(
            'stats',
            'recentCampaigns',
            'recentPages',
            'recentExports',
            'campaignStatusStats'
        ));
    }
}
