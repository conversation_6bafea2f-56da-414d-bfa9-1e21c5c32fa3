<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Campaign;
use App\Models\ExportLog;
use App\Models\GeneratedPage;
use App\Services\WordPressExportService;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ExportController extends Controller
{
    protected WordPressExportService $exportService;

    public function __construct(WordPressExportService $exportService)
    {
        $this->exportService = $exportService;
    }

    /**
     * Show export manager interface.
     */
    public function index(): View
    {
        $exportLogs = ExportLog::with('campaign')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('export.index', compact('exportLogs'));
    }

    /**
     * Show export form for a campaign.
     */
    public function create(Campaign $campaign, Request $request): View
    {
        $campaign->load('wordpressConnection');

        $generatedPages = $campaign->generatedPages()
            ->whereIn('status', ['generated', 'exported'])
            ->get();

        // Get pre-selected page ID if provided
        $preSelectedPageId = $request->get('page_id');

        return view('export.create', compact('campaign', 'generatedPages', 'preSelectedPageId'));
    }

    /**
     * Start export process to WordPress.
     */
    public function store(Request $request, Campaign $campaign): RedirectResponse
    {
        Log::info('Export store method called', [
            'campaign_id' => $campaign->id,
            'request_data' => $request->all()
        ]);

        $validated = $request->validate([
            'page_ids' => 'required|array',
            'page_ids.*' => 'exists:generated_pages,id',
        ]);

        Log::info('Validation passed', [
            'validated_data' => $validated
        ]);

        if (!$campaign->wordpressConnection) {
            Log::error('No WordPress connection found for campaign', [
                'campaign_id' => $campaign->id
            ]);
            return back()->withErrors(['connection' => 'This campaign does not have a WordPress connection. Please edit the campaign to set one.']);
        }

        Log::info('WordPress connection found', [
            'connection_id' => $campaign->wordpressConnection->id,
            'connection_url' => $campaign->wordpressConnection->url
        ]);

        try {
            $exportLog = $this->exportService->exportPages($campaign, $validated['page_ids']);

            Log::info('Export completed', [
                'export_log_id' => $exportLog->id
            ]);

            return redirect()->route('export.show', $exportLog)
                ->with('success', 'Export process started successfully!');
        } catch (\Exception $e) {
            Log::error('Exception in export store', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return back()->withErrors(['connection' => $e->getMessage()]);
        }
    }

    /**
     * Show export log details.
     */
    public function show(ExportLog $exportLog): View
    {
        $exportLog->load('campaign');

        return view('export.show', compact('exportLog'));
    }

    /**
     * Process export to WordPress.
     */
    private function processExport(ExportLog $exportLog, $generatedPages): void
    {
        $exportLog->markAsStarted();

        $settings = $exportLog->export_settings;
        $wordpressUrl = rtrim($settings['wordpress_url'], '/');

        try {
            foreach ($generatedPages as $page) {
                try {
                    $this->exportSinglePage($page, $wordpressUrl, $settings);
                    $page->update(['status' => 'exported']);
                    $exportLog->incrementExported();
                } catch (\Exception $e) {
                    $exportLog->incrementFailed();
                    \Log::error("Export failed for page {$page->id}: " . $e->getMessage());
                }
            }

            $exportLog->markAsCompleted();

        } catch (\Exception $e) {
            $exportLog->markAsFailed($e->getMessage());
        }
    }

    /**
     * Export single page to WordPress.
     */
    private function exportSinglePage(GeneratedPage $page, string $wordpressUrl, array $settings): void
    {
        $content = $settings['export_shortcodes'] ? $page->html_with_shortcodes : $page->raw_html;

        $postData = [
            'title' => $page->title,
            'content' => $content,
            'status' => $settings['post_status'],
            'type' => $settings['post_type'],
            'slug' => $page->slug,
        ];

        // Add SEO data if available
        if ($page->seo_data) {
            $postData['meta'] = [
                '_yoast_wpseo_title' => $page->seo_data['title'] ?? '',
                '_yoast_wpseo_metadesc' => $page->seo_data['description'] ?? '',
                '_yoast_wpseo_focuskw' => $page->seo_data['focus_keyword'] ?? '',
            ];

            // Add schema markup
            if (isset($page->seo_data['schema']) && !empty($page->seo_data['schema'])) {
                $postData['meta']['_yoast_wpseo_schema'] = json_encode($page->seo_data['schema']);
            }
        }

        // In a real implementation, this would use WordPress REST API
        $response = Http::withBasicAuth($settings['wordpress_username'], $settings['wordpress_password'])
            ->post($wordpressUrl . '/wp-json/wp/v2/' . $settings['post_type'] . 's', $postData);

        if (!$response->successful()) {
            throw new \Exception('WordPress API error: ' . $response->body());
        }

        $responseData = $response->json();
        $page->update(['wordpress_post_id' => $responseData['id'] ?? null]);

        // If exporting shortcodes, also send shortcode mapping to LaraPageGenerator plugin
        if ($settings['export_shortcodes'] && $page->shortcode_mapping) {
            $this->sendShortcodeMapping($page, $wordpressUrl, $settings);
        }
    }

    /**
     * Send shortcode mapping to LaraPageGenerator plugin.
     */
    private function sendShortcodeMapping(GeneratedPage $page, string $wordpressUrl, array $settings): void
    {
        $mappingData = [
            'post_id' => $page->wordpress_post_id,
            'campaign_id' => $page->campaign_id,
            'shortcode_mapping' => $page->shortcode_mapping,
        ];

        // Send to custom plugin endpoint
        Http::withBasicAuth($settings['wordpress_username'], $settings['wordpress_password'])
            ->post($wordpressUrl . '/wp-json/lara-page-generator/v1/shortcodes', $mappingData);
    }

    /**
     * Get export progress via AJAX.
     */
    public function getProgress(ExportLog $exportLog): JsonResponse
    {
        $progress = $this->exportService->getProgress($exportLog);
        return response()->json($progress);
    }

    /**
     * Test WordPress connection via AJAX.
     */
    public function testConnection(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'wordpress_url' => 'required|url',
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $result = $this->exportService->testConnection($validated);
        return response()->json($result);
    }

    /**
     * Get WordPress categories via AJAX.
     */
    public function getCategories(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'wordpress_url' => 'required|url',
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        $result = $this->exportService->getCategories($validated);
        return response()->json($result);
    }
}
