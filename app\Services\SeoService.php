<?php

namespace App\Services;

use App\Models\GeneratedPage;
use App\Models\Campaign;

class SeoService
{
    /**
     * Generate comprehensive SEO data for a page.
     */
    public function generateSeoData(GeneratedPage $page, array $pageData = null): array
    {
        $pageData = $pageData ?? $page->page_data;
        $campaign = $page->campaign;
        
        $seoData = [
            'title' => $this->generateTitle($pageData, $campaign),
            'description' => $this->generateDescription($pageData, $campaign),
            'focus_keyword' => $this->generateFocusKeyword($pageData, $campaign),
            'canonical_url' => $this->generateCanonicalUrl($page),
            'og_data' => $this->generateOpenGraphData($pageData, $campaign),
            'twitter_data' => $this->generateTwitterCardData($pageData, $campaign),
            'schema' => $this->generateAllSchemas($pageData, $campaign),
            'meta_robots' => 'index, follow',
            'breadcrumbs' => $this->generateBreadcrumbs($page),
        ];
        
        return $seoData;
    }
    
    /**
     * Generate optimized title tag.
     */
    private function generateTitle(array $pageData, Campaign $campaign): string
    {
        // Priority: Yoast title > Hero headline + business name > fallback
        if (isset($pageData['Yoast_title']) && !empty($pageData['Yoast_title'])) {
            return $this->optimizeTitle($pageData['Yoast_title']);
        }
        
        if (isset($pageData['Hero_headline'])) {
            $title = $pageData['Hero_headline'];
            if ($campaign->business_name) {
                $title .= ' | ' . $campaign->business_name;
            }
            return $this->optimizeTitle($title);
        }
        
        return $this->optimizeTitle($campaign->business_name . ' - ' . $campaign->main_keyword);
    }
    
    /**
     * Generate meta description.
     */
    private function generateDescription(array $pageData, Campaign $campaign): string
    {
        if (isset($pageData['Yoast_description']) && !empty($pageData['Yoast_description'])) {
            return $this->optimizeDescription($pageData['Yoast_description']);
        }
        
        $description = '';
        if (isset($pageData['Hero_description'])) {
            $description = $pageData['Hero_description'];
        } elseif ($campaign->business_description) {
            $description = $campaign->business_description;
        }
        
        // Add business name and location if available
        if ($campaign->business_name) {
            $description .= ' - ' . $campaign->business_name;
        }
        
        if ($campaign->address) {
            $description .= ', ' . $campaign->address;
        }
        
        return $this->optimizeDescription($description);
    }
    
    /**
     * Generate focus keyword.
     */
    private function generateFocusKeyword(array $pageData, Campaign $campaign): string
    {
        if (isset($pageData['Yoast_focus_keyword']) && !empty($pageData['Yoast_focus_keyword'])) {
            return $pageData['Yoast_focus_keyword'];
        }
        
        return $campaign->main_keyword;
    }
    
    /**
     * Generate canonical URL.
     */
    private function generateCanonicalUrl(GeneratedPage $page): string
    {
        return url('/' . $page->slug);
    }
    
    /**
     * Generate Open Graph data.
     */
    private function generateOpenGraphData(array $pageData, Campaign $campaign): array
    {
        return [
            'og:type' => 'website',
            'og:title' => $this->generateTitle($pageData, $campaign),
            'og:description' => $this->generateDescription($pageData, $campaign),
            'og:site_name' => $campaign->business_name,
            'og:locale' => 'id_ID',
        ];
    }
    
    /**
     * Generate Twitter Card data.
     */
    private function generateTwitterCardData(array $pageData, Campaign $campaign): array
    {
        return [
            'twitter:card' => 'summary_large_image',
            'twitter:title' => $this->generateTitle($pageData, $campaign),
            'twitter:description' => $this->generateDescription($pageData, $campaign),
        ];
    }
    
    /**
     * Generate all schema markups.
     */
    private function generateAllSchemas(array $pageData, Campaign $campaign): array
    {
        $schemas = [];
        
        // FAQ Schema
        if (isset($pageData['FAQ']) && is_array($pageData['FAQ']) && !empty($pageData['FAQ'])) {
            $schemas[] = $this->generateFaqSchema($pageData['FAQ']);
        }
        
        // Local Business Schema
        $schemas[] = $this->generateLocalBusinessSchema($campaign, $pageData);
        
        // Website Schema
        $schemas[] = $this->generateWebsiteSchema($campaign);
        
        // Breadcrumb Schema
        $schemas[] = $this->generateBreadcrumbSchema($campaign);
        
        return $schemas;
    }
    
    /**
     * Generate FAQ Schema.
     */
    private function generateFaqSchema(array $faqs): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'FAQPage',
            'mainEntity' => array_map(function ($faq) {
                return [
                    '@type' => 'Question',
                    'name' => $faq['q'],
                    'acceptedAnswer' => [
                        '@type' => 'Answer',
                        'text' => $faq['a']
                    ]
                ];
            }, $faqs)
        ];
    }
    
    /**
     * Generate Local Business Schema.
     */
    private function generateLocalBusinessSchema(Campaign $campaign, array $pageData): array
    {
        $schema = [
            '@context' => 'https://schema.org',
            '@type' => 'LocalBusiness',
            'name' => $campaign->business_name,
            'description' => $campaign->business_description,
        ];
        
        if ($campaign->contact_phone) {
            $schema['telephone'] = $campaign->contact_phone;
        }
        
        if ($campaign->contact_email) {
            $schema['email'] = $campaign->contact_email;
        }
        
        if ($campaign->address) {
            $schema['address'] = [
                '@type' => 'PostalAddress',
                'streetAddress' => $campaign->address
            ];
        }
        
        // Add service offered
        $schema['hasOfferCatalog'] = [
            '@type' => 'OfferCatalog',
            'name' => 'Services',
            'itemListElement' => [
                [
                    '@type' => 'Offer',
                    'itemOffered' => [
                        '@type' => 'Service',
                        'name' => $campaign->main_keyword
                    ]
                ]
            ]
        ];
        
        return $schema;
    }
    
    /**
     * Generate Website Schema.
     */
    private function generateWebsiteSchema(Campaign $campaign): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => $campaign->business_name,
            'url' => url('/'),
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => url('/search?q={search_term_string}'),
                'query-input' => 'required name=search_term_string'
            ]
        ];
    }
    
    /**
     * Generate Breadcrumb Schema.
     */
    private function generateBreadcrumbSchema(Campaign $campaign): array
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => [
                [
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => 'Home',
                    'item' => url('/')
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => $campaign->main_keyword,
                    'item' => url('/' . $campaign->main_keyword)
                ]
            ]
        ];
    }
    
    /**
     * Generate breadcrumbs for display.
     */
    private function generateBreadcrumbs(GeneratedPage $page): array
    {
        return [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => $page->campaign->main_keyword, 'url' => null],
            ['name' => $page->title, 'url' => null]
        ];
    }
    
    /**
     * Optimize title length and format.
     */
    private function optimizeTitle(string $title): string
    {
        // Remove extra spaces and trim
        $title = trim(preg_replace('/\s+/', ' ', $title));
        
        // Limit to 60 characters for SEO
        if (strlen($title) > 60) {
            $title = substr($title, 0, 57) . '...';
        }
        
        return $title;
    }
    
    /**
     * Optimize description length and format.
     */
    private function optimizeDescription(string $description): string
    {
        // Remove extra spaces and trim
        $description = trim(preg_replace('/\s+/', ' ', $description));
        
        // Limit to 160 characters for SEO
        if (strlen($description) > 160) {
            $description = substr($description, 0, 157) . '...';
        }
        
        return $description;
    }
    
    /**
     * Generate meta tags HTML.
     */
    public function generateMetaTags(array $seoData): string
    {
        $html = '';
        
        // Basic meta tags
        $html .= '<title>' . htmlspecialchars($seoData['title']) . '</title>' . "\n";
        $html .= '<meta name="description" content="' . htmlspecialchars($seoData['description']) . '">' . "\n";
        $html .= '<meta name="robots" content="' . $seoData['meta_robots'] . '">' . "\n";
        $html .= '<link rel="canonical" href="' . $seoData['canonical_url'] . '">' . "\n";
        
        // Open Graph tags
        foreach ($seoData['og_data'] as $property => $content) {
            $html .= '<meta property="' . $property . '" content="' . htmlspecialchars($content) . '">' . "\n";
        }
        
        // Twitter Card tags
        foreach ($seoData['twitter_data'] as $name => $content) {
            $html .= '<meta name="' . $name . '" content="' . htmlspecialchars($content) . '">' . "\n";
        }
        
        // Schema markup
        if (!empty($seoData['schema'])) {
            $html .= '<script type="application/ld+json">' . "\n";
            $html .= json_encode($seoData['schema'], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
            $html .= "\n" . '</script>' . "\n";
        }
        
        return $html;
    }
}
