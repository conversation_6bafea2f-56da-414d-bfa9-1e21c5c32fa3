<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CampaignController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\ContentGeneratorController;
use App\Http\Controllers\WordPressExportController;
use App\Http\Controllers\WordPressConnectionController;
use App\Http\Controllers\ExportController;
use App\Http\Controllers\SettingsController;

// Redirect root to dashboard
Route::get('/', function () {
    return redirect()->route('dashboard');
});


// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// Campaigns
Route::resource('campaigns', CampaignController::class);
Route::get('/campaigns/{campaign}/master-prompt', [CampaignController::class, 'showMasterPrompt'])
    ->name('campaigns.master-prompt');
Route::delete('/campaigns/{campaign}/pages/{page}', [CampaignController::class, 'deletePage'])
    ->name('campaigns.delete-page');
Route::delete('/campaigns/{campaign}/pages', [CampaignController::class, 'deleteSelectedPages'])
    ->name('campaigns.delete-selected-pages');
Route::delete('/campaigns/{campaign}/all-pages', [CampaignController::class, 'deleteAllPages'])
    ->name('campaigns.delete-all-pages');

// Templates
Route::resource('templates', TemplateController::class);
Route::get('/templates/{template}/preview', [TemplateController::class, 'preview'])
    ->name('templates.preview');
Route::post('/templates/{template}/process', [TemplateController::class, 'process'])
    ->name('templates.process');



// Content Generator
Route::get('/content-generator', [ContentGeneratorController::class, 'index'])
    ->name('content-generator.index');
Route::get('/content-generator/{campaign}/upload', [ContentGeneratorController::class, 'upload'])
    ->name('content-generator.upload');
Route::post('/content-generator/{campaign}/process', [ContentGeneratorController::class, 'processJson'])
    ->name('content-generator.process');
Route::get('/content-generator/pages/{page}/preview', [ContentGeneratorController::class, 'preview'])
    ->name('content-generator.preview');
Route::get('/content-generator/pages/{page}/preview-window', [ContentGeneratorController::class, 'previewWindow'])
    ->name('content-generator.preview-window');
Route::get('/content-generator/pages/{page}/complete-html', [ContentGeneratorController::class, 'getCompleteHtml'])
    ->name('content-generator.get-complete-html');
Route::get('/content-generator/{campaign}/master-prompt', [ContentGeneratorController::class, 'getMasterPrompt'])
    ->name('content-generator.master-prompt');
Route::post('/content-generator/{campaign}/regenerate-prompt', [ContentGeneratorController::class, 'regeneratePrompt'])
    ->name('content-generator.regenerate-prompt');

// Export Manager
Route::get('/export', [ExportController::class, 'index'])->name('export.index');
Route::get('/export/{campaign}/create', [ExportController::class, 'create'])->name('export.create');
Route::post('/export/{campaign}', [ExportController::class, 'store'])->name('export.store');
Route::get('/export/{exportLog}', [ExportController::class, 'show'])->name('export.show');
Route::get('/export/{exportLog}/progress', [ExportController::class, 'getProgress'])
    ->name('export.progress');
Route::post('/export/test-connection', [ExportController::class, 'testConnection'])
    ->name('export.test-connection');

// WordPress Integration
Route::prefix('wordpress')->name('wordpress.')->group(function () {
    // WordPress Connections Management
    Route::resource('connections', WordPressConnectionController::class);
    Route::post('/connections/{connection}/test', [WordPressConnectionController::class, 'testConnection'])->name('connections.test');
    Route::post('/connections/test-new', [WordPressConnectionController::class, 'testNewConnection'])->name('connections.test-new');
    Route::post('/connections/{connection}/set-default', [WordPressConnectionController::class, 'setDefault'])->name('connections.set-default');
    Route::post('/connections/{connection}/toggle-active', [WordPressConnectionController::class, 'toggleActive'])->name('connections.toggle-active');

    // Legacy Export Routes (will be updated to use connections)
    Route::get('/settings', [WordPressExportController::class, 'showSettings'])->name('settings');
    Route::post('/test-connection', [WordPressExportController::class, 'testConnection'])->name('test-connection');
    Route::post('/export/page/{page}', [WordPressExportController::class, 'exportPage'])->name('export.page');
    Route::post('/export/campaign/{campaign}/selected', [WordPressExportController::class, 'exportSelectedPages'])->name('export.selected');
    Route::post('/export/campaign/{campaign}/all', [WordPressExportController::class, 'exportAllPages'])->name('export.all');
    Route::post('/bulk-export', [WordPressExportController::class, 'bulkExport'])->name('bulk-export');
    Route::get('/debug-export/{page}', [WordPressExportController::class, 'debugExport'])->name('debug-export');
});
Route::post('/export/get-categories', [ExportController::class, 'getCategories'])
    ->name('export.get-categories');

// Settings
Route::prefix('settings')->name('settings.')->group(function () {
    Route::get('/', [SettingsController::class, 'index'])->name('index');
    Route::put('/', [SettingsController::class, 'update'])->name('update');
});
