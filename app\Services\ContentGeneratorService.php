<?php

namespace App\Services;

use App\Models\Campaign;
use App\Models\Template;
use App\Models\GeneratedPage;
use Illuminate\Support\Str;

class ContentGeneratorService
{
    protected SeoService $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }
    
    /**
     * Process JSON data and generate pages.
     */
    public function processJsonData(Campaign $campaign, Template $template, array $jsonData): array
    {
        $results = [
            'success' => 0,
            'errors' => [],
            'pages' => []
        ];
        
        foreach ($jsonData as $index => $pageData) {
            try {
                $page = $this->generateSinglePage($campaign, $template, $pageData, $index);
                $results['pages'][] = $page;
                $results['success']++;
            } catch (\Exception $e) {
                $results['errors'][] = "Error on item {$index}: " . $e->getMessage();
            }
        }
        
        return $results;
    }
    
    /**
     * Generate a single page from data.
     */
    public function generateSinglePage(Campaign $campaign, Template $template, array $pageData, int $index = 0): GeneratedPage
    {
        // Validate required data
        $errors = $this->validateTemplateData($template, $pageData);
        if (!empty($errors)) {
            throw new \Exception(implode(', ', $errors));
        }
        
        // Merge campaign data with page data
        $mergedData = $this->mergeCampaignData($campaign, $pageData);

        // Create temporary page for SEO generation
        $tempPage = new GeneratedPage([
            'campaign_id' => $campaign->id,
            'template_id' => $template->id,
            'title' => $this->generateTitle($mergedData),
            'page_data' => $pageData,
        ]);
        $tempPage->campaign = $campaign;

        // Generate comprehensive SEO data
        $seoData = $this->seoService->generateSeoData($tempPage, $mergedData);

        // Generate HTML content with SEO
        if ($template->hasHeadMainSeparation()) {
            // For head/main separated templates, use specialized method
            $rawHtml = $this->processHeadMainTemplate($template, $mergedData, $seoData);
        } else {
            // For legacy templates, use old method
            $rawHtml = $this->processTemplate($template, $mergedData, $seoData);
        }
        
        // Create the page
        $page = GeneratedPage::create([
            'campaign_id' => $campaign->id,
            'template_id' => $template->id,
            'title' => $this->generateTitle($mergedData),
            'slug' => '', // Will be set after creation
            'raw_html' => $rawHtml,
            'html_with_shortcodes' => $rawHtml, // No shortcodes anymore, just use raw HTML
            'page_data' => $pageData,
            'seo_data' => $seoData,
            'shortcode_mapping' => [], // No shortcode mapping needed
            'status' => 'generated',
        ]);
        
        // Generate unique slug
        $page->slug = $this->generateUniqueSlug($page);
        $page->save();
        
        return $page;
    }
    
    /**
     * Merge campaign data with page data.
     */
    private function mergeCampaignData(Campaign $campaign, array $pageData): array
    {
        $campaignData = [
            'Business_name' => $campaign->business_name,
            'Main_keyword' => $campaign->main_keyword,
            'Contact' => $campaign->contact_phone,
            'Contact_phone' => $campaign->contact_phone,
            'Contact_email' => $campaign->contact_email,
            'Address' => $campaign->address,
            'Business_description' => $campaign->business_description,
        ];
        
        // Page data takes precedence over campaign data
        return array_merge($campaignData, $pageData);
    }
    

    
    /**
     * Generate page title from data.
     */
    private function generateTitle(array $data): string
    {
        if (isset($data['Yoast_title']) && !empty($data['Yoast_title'])) {
            return $data['Yoast_title'];
        }
        
        if (isset($data['Hero_headline']) && !empty($data['Hero_headline'])) {
            return $data['Hero_headline'];
        }
        
        return 'Generated Page';
    }
    
    /**
     * Generate unique slug for the page.
     */
    private function generateUniqueSlug(GeneratedPage $page): string
    {
        $baseSlug = Str::slug($page->title);
        $slug = $baseSlug;
        $counter = 1;
        
        while (GeneratedPage::where('slug', $slug)->where('id', '!=', $page->id)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * Validate JSON structure.
     */
    public function validateJsonStructure(array $jsonData): array
    {
        $errors = [];
        
        if (!is_array($jsonData)) {
            $errors[] = 'JSON must be an array';
            return $errors;
        }
        
        foreach ($jsonData as $index => $item) {
            if (!is_array($item)) {
                $errors[] = "Item {$index} must be an object";
                continue;
            }
            
            // Check for required fields
            if (!isset($item['Hero_headline']) || empty($item['Hero_headline'])) {
                $errors[] = "Item {$index} is missing Hero_headline";
            }
            
            // Validate FAQ structure if present
            if (isset($item['FAQ'])) {
                if (!is_array($item['FAQ'])) {
                    $errors[] = "Item {$index}: FAQ must be an array";
                } else {
                    foreach ($item['FAQ'] as $faqIndex => $faq) {
                        if (!is_array($faq) || !isset($faq['q']) || !isset($faq['a'])) {
                            $errors[] = "Item {$index}: FAQ item {$faqIndex} must have 'q' and 'a' properties";
                        }
                    }
                }
            }
        }
        
        return $errors;
    }
    
    /**
     * Generate master prompt for campaign.
     */
    public function generateMasterPrompt(Campaign $campaign): string
    {
        $prompt = "Generate landing page content for the following business:\n\n";
        $prompt .= "Business Name: {$campaign->business_name}\n";
        $prompt .= "Main Keyword: {$campaign->main_keyword}\n";
        $prompt .= "Business Description: {$campaign->business_description}\n";
        
        if ($campaign->contact_phone) {
            $prompt .= "Contact Phone: {$campaign->contact_phone}\n";
        }
        
        if ($campaign->contact_email) {
            $prompt .= "Contact Email: {$campaign->contact_email}\n";
        }
        
        if ($campaign->address) {
            $prompt .= "Address: {$campaign->address}\n";
        }
        
        if ($campaign->people_also_ask && count($campaign->people_also_ask) > 0) {
            $prompt .= "\nFAQ Questions (People Also Ask):\n";
            foreach ($campaign->people_also_ask as $index => $question) {
                $prompt .= ($index + 1) . ". {$question}\n";
            }
        }
        
        $prompt .= "\nPlease generate a JSON array with multiple variations of landing page content. Each object should include ALL of the following fields:\n\n";

        // SEO Fields
        $prompt .= "SEO FIELDS:\n";
        $prompt .= "- Yoast_title: SEO title (max 60 characters)\n";
        $prompt .= "- Yoast_description: Meta description (max 160 characters)\n\n";

        // Hero Section
        $prompt .= "HERO SECTION:\n";
        $prompt .= "- Hero_headline: Main headline for the hero section\n";
        $prompt .= "- Hero_description: Detailed description for hero section\n";
        $prompt .= "- Trust_badge: Trust indicator text (e.g., 'Dipercaya oleh 500+ Pelanggan')\n\n";

        // Benefits Section (6 benefits)
        $prompt .= "BENEFITS SECTION (6 benefits):\n";
        for ($i = 1; $i <= 6; $i++) {
            $prompt .= "- Benefit_{$i}_title: Title for benefit {$i}\n";
            $prompt .= "- Benefit_{$i}_description: Description for benefit {$i}\n";
        }
        $prompt .= "\n";

        // Pricing Section (3 packages)
        $prompt .= "PRICING SECTION (3 packages):\n";
        for ($i = 1; $i <= 3; $i++) {
            $prompt .= "- Package_{$i}_name: Name for package {$i}\n";
            $prompt .= "- Package_{$i}_price: Price for package {$i}\n";
            $prompt .= "- Package_{$i}_description: Description for package {$i}\n";
        }
        $prompt .= "\n";

        // FAQ Section (8 FAQs)
        $prompt .= "FAQ SECTION (8 FAQs):\n";
        for ($i = 1; $i <= 8; $i++) {
            $prompt .= "- FAQ_{$i}_question: Question for FAQ {$i}\n";
            $prompt .= "- FAQ_{$i}_answer: Answer for FAQ {$i}\n";
        }
        $prompt .= "\n";

        // Services Section (4 services)
        $prompt .= "SERVICES SECTION (4 services):\n";
        for ($i = 1; $i <= 4; $i++) {
            $prompt .= "- Service_{$i}_title: Title for service {$i}\n";
            $prompt .= "- Service_{$i}_description: Description for service {$i}\n";
        }
        $prompt .= "\n";

        // Contact & Business Info
        $prompt .= "CONTACT & BUSINESS INFO:\n";
        $prompt .= "- Business_name: Business name\n";
        $prompt .= "- Contact_phone: Phone number (format: 628xxxxxxxxxx)\n";
        $prompt .= "- Contact_email: Email address\n";
        $prompt .= "- Contact_whatsapp: WhatsApp number (format: 628xxxxxxxxxx)\n\n";

        // CTA Section
        $prompt .= "CTA SECTION:\n";
        $prompt .= "- CTA_headline: Call-to-action headline\n";
        $prompt .= "- CTA_description: Call-to-action description\n\n";
        
        $prompt .= "Example JSON structure:\n";
        $prompt .= json_encode([
            [
                "Hero_headline" => "Example Headline",
                "FAQ" => [
                    ["q" => "Example question?", "a" => "Example answer."]
                ],
                "Yoast_title" => "Example SEO Title",
                "Yoast_description" => "Example meta description for SEO",
                "Yoast_focus_keyword" => "example keyword",
                "Contact" => $campaign->contact_phone ?? "+62 xxx-xxxx-xxxx",
                "Address" => $campaign->address ?? "Your Address"
            ]
        ], JSON_PRETTY_PRINT);
        
        return $prompt;
    }
    
    /**
     * Preview generated page HTML.
     */
    public function previewPage(GeneratedPage $page): string
    {
        // Add some basic styling for preview
        $html = $page->html_with_shortcodes;
        
        // Inject preview styles if not already present
        if (strpos($html, 'preview-styles') === false) {
            $previewStyles = '<style id="preview-styles">
                body { margin: 0; padding: 20px; font-family: system-ui, -apple-system, sans-serif; }
                .container { max-width: 1200px; margin: 0 auto; }
                .preview-header { background: #f3f4f6; padding: 10px; margin-bottom: 20px; border-radius: 8px; }
                .preview-header h3 { margin: 0; color: #374151; }
            </style>';
            
            $html = str_replace('</head>', $previewStyles . '</head>', $html);
        }
        
        return $html;
    }

    /**
     * Process master prompt from template with campaign data.
     */
    public function processMasterPrompt(Template $template, Campaign $campaign): string
    {
        // Use template master prompt or generate fallback
        $prompt = $template->master_prompt ?? $this->generateFallbackMasterPrompt();

        // Replace placeholders with campaign data
        $replacements = [
            '{business_name}' => $campaign->business_name,
            '{main_keyword}' => $campaign->main_keyword,
            '{business_description}' => $campaign->business_description,
            '{contact_phone}' => $campaign->contact_phone ?? '',
            '{contact_email}' => $campaign->contact_email ?? '',
            '{address}' => $campaign->address ?? '',
        ];

        // Add keywords and FAQ questions information
        $keywordsInfo = $this->buildKeywordsInfo($campaign);
        $replacements['{faq_questions}'] = $keywordsInfo;

        return str_replace(array_keys($replacements), array_values($replacements), $prompt);
    }

    /**
     * Generate fallback master prompt when template doesn't have one.
     */
    private function generateFallbackMasterPrompt(): string
    {
        return 'You are an expert content creator for professional business landing pages. Generate compelling, conversion-focused content for a comprehensive business landing page.

Business Context:
- Business Name: {business_name}
- Main Keyword: {main_keyword}
- Business Description: {business_description}
- Contact Phone: {contact_phone}
- Contact Email: {contact_email}
- Address: {address}

Target Audience: Small to medium businesses looking for professional services
Goal: Generate high-converting landing page content

Please create content that includes all the required fields for the template placeholders. Make the content engaging, professional, and optimized for conversions.

Required JSON structure with these exact field names:
{
  "Yoast_title": "SEO optimized title (max 60 characters)",
  "Yoast_description": "SEO meta description (max 160 characters)",
  "Hero_headline": "Compelling main headline",
  "Hero_description": "Supporting description for hero section",
  "Trust_badge": "Trust indicator text",
  "Client_count": "Number of clients",
  "Client_count_label": "Label for client count",
  "Support_hours": "Support availability",
  "Support_label": "Label for support",
  "Success_rate": "Success percentage",
  "Success_label": "Label for success rate",
  "Delivery_time": "Delivery timeframe",
  "Delivery_label": "Label for delivery",
  "Social_proof_title": "Social proof section title",
  "Social_proof_description": "Social proof description",
  "Intro_title": "Introduction section title",
  "Intro_description": "Introduction description",
  "Benefit_1_title": "First benefit title",
  "Benefit_1_description": "First benefit description",
  "Benefit_2_title": "Second benefit title",
  "Benefit_2_description": "Second benefit description",
  "Benefit_3_title": "Third benefit title",
  "Benefit_3_description": "Third benefit description",
  "Benefit_4_title": "Fourth benefit title",
  "Benefit_4_description": "Fourth benefit description",
  "Benefit_5_title": "Fifth benefit title",
  "Benefit_5_description": "Fifth benefit description",
  "Benefit_6_title": "Sixth benefit title",
  "Benefit_6_description": "Sixth benefit description",
  "Feature_1_title": "First feature title",
  "Feature_1_description": "First feature description",
  "Feature_2_title": "Second feature title",
  "Feature_2_description": "Second feature description",
  "Feature_3_title": "Third feature title",
  "Feature_3_description": "Third feature description",
  "Feature_4_title": "Fourth feature title",
  "Feature_4_description": "Fourth feature description",
  "Feature_5_title": "Fifth feature title",
  "Feature_5_description": "Fifth feature description",
  "Feature_6_title": "Sixth feature title",
  "Feature_6_description": "Sixth feature description",
  "Pricing_title": "Pricing section title",
  "Pricing_description": "Pricing section description",
  "FAQ_1_question": "First FAQ question",
  "FAQ_1_answer": "First FAQ answer",
  "FAQ_2_question": "Second FAQ question",
  "FAQ_2_answer": "Second FAQ answer",
  "FAQ_3_question": "Third FAQ question",
  "FAQ_3_answer": "Third FAQ answer",
  "FAQ_4_question": "Fourth FAQ question",
  "FAQ_4_answer": "Fourth FAQ answer",
  "FAQ_5_question": "Fifth FAQ question",
  "FAQ_5_answer": "Fifth FAQ answer",
  "Cta_title": "Call to action title",
  "Cta_description": "Call to action description",
  "Cta_button_text": "Call to action button text",
  "Business_name": "{business_name}",
  "Contact": "{contact_phone}",
  "Contact_email": "{contact_email}",
  "Address": "{address}"
}

Generate content in Indonesian language that is professional, engaging, and conversion-focused.';
    }



    /**
     * Build keywords and FAQ information for master prompt.
     */
    private function buildKeywordsInfo(Campaign $campaign): string
    {
        $info = '';

        // Check if we have structured keywords data
        if ($campaign->keywords_data && is_array($campaign->keywords_data)) {
            $info .= "\nKeywords and FAQ Information:\n";

            foreach ($campaign->keywords_data as $index => $keywordData) {
                $keyword = $keywordData['keyword'] ?? '';
                $faqs = $keywordData['faqs'] ?? [];

                $info .= "\nKeyword #" . ($index + 1) . ": {$keyword}\n";

                if (!empty($faqs)) {
                    $info .= "FAQ Questions for '{$keyword}':\n";
                    foreach ($faqs as $faq) {
                        $info .= "- {$faq}\n";
                    }
                }
            }

            $keywordCount = count($campaign->keywords_data);
            $info .= "\nIMPORTANT: Generate content for ALL {$keywordCount} keywords. Create separate content objects for each keyword, ensuring each is unique and tailored to its specific keyword while maintaining consistency with the business context.\n";

        } else {
            // Fallback to old format
            if ($campaign->people_also_ask && count($campaign->people_also_ask) > 0) {
                $info .= "\nFAQ Questions to address:\n";
                foreach ($campaign->people_also_ask as $question) {
                    $info .= "- {$question}\n";
                }
            }
        }

        return $info;
    }

    /**
     * Validate template data against template placeholders.
     */
    private function validateTemplateData(Template $template, array $pageData): array
    {
        $errors = [];

        if (!$template->placeholders || !is_array($template->placeholders)) {
            return $errors; // No validation needed if no placeholders
        }

        // Check if all required placeholders have data
        foreach ($template->placeholders as $placeholder => $originalText) {
            if (!isset($pageData[$placeholder]) || empty($pageData[$placeholder])) {
                $errors[] = "Missing data for placeholder: {$placeholder}";
            }
        }

        return $errors;
    }

    /**
     * Process template with data and SEO for legacy templates.
     */
    private function processTemplate(Template $template, array $data, array $seoData): string
    {
        // Generate HTML from template
        $html = $template->replacePlaceholders($data);

        // Add SEO data to HTML
        $html = $this->injectSeoData($html, $seoData);

        return $html;
    }

    /**
     * Process head/main separated template (not implemented yet).
     */
    private function processHeadMainTemplate(Template $template, array $data, array $seoData): string
    {
        // For now, fallback to legacy method
        return $this->processTemplate($template, $data, $seoData);
    }

    /**
     * Inject SEO data into HTML.
     */
    private function injectSeoData(string $html, array $seoData): string
    {
        // Replace SEO placeholders
        $replacements = [
            '{{Yoast_title}}' => $seoData['title'] ?? 'Default Title',
            '{{Yoast_description}}' => $seoData['description'] ?? 'Default Description',
        ];

        foreach ($replacements as $placeholder => $value) {
            $html = str_replace($placeholder, $value, $html);
        }

        return $html;
    }
}
