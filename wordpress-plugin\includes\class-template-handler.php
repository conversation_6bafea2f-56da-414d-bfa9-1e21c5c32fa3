<?php
/**
 * Template Handler Class
 * 
 * Handles template loading, head optimization, and content rendering
 * for landing pages with proper SEO structure
 */

if (!defined('ABSPATH')) {
    exit;
}

class PageGenerator_Template_Handler {
    
    public function __construct() {
        add_filter('template_include', array($this, 'load_landing_page_template'));
        add_action('wp_head', array($this, 'remove_duplicate_head_elements'), 0);
        add_action('wp_head', array($this, 'optimize_head_structure'), 2);
    }

    /**
     * Load custom template for landing pages
     */
    public function load_landing_page_template($template) {
        if (is_singular('landing_page')) {
            $custom_template = plugin_dir_path(dirname(__FILE__)) . 'templates/single-landing_page.php';
            if (file_exists($custom_template)) {
                return $custom_template;
            }
        }
        return $template;
    }

    /**
     * Remove duplicate head elements that might be added by theme or other plugins
     */
    public function remove_duplicate_head_elements() {
        if (!is_singular('landing_page')) {
            return;
        }

        // Remove duplicate meta tags
        remove_action('wp_head', 'wp_generator');
        remove_action('wp_head', 'wlwmanifest_link');
        remove_action('wp_head', 'rsd_link');
        remove_action('wp_head', 'wp_shortlink_wp_head');
        
        // Remove unnecessary feed links for landing pages
        remove_action('wp_head', 'feed_links', 2);
        remove_action('wp_head', 'feed_links_extra', 3);
    }

    /**
     * Optimize head structure for better SEO
     * DISABLED: This method is now handled by the template-based approach
     * to prevent duplicate head content injection
     */
    public function optimize_head_structure() {
        // Disabled in favor of template-based approach
        // The single-landing_page.php template now handles head content injection
        // with proper placeholder replacement and filtering
        return;
    }

    /**
     * Inject optimized head content
     */
    private function inject_optimized_head_content($page_data, $post) {
        // Extract and clean head content
        $head_content = isset($page_data['head_content']) ? $page_data['head_content'] : '';
        
        if (!empty($head_content)) {
            // Remove duplicate meta charset and viewport (WordPress already adds these)
            $head_content = preg_replace('/<meta\s+charset[^>]*>/i', '', $head_content);
            $head_content = preg_replace('/<meta\s+name=["\']viewport["\'][^>]*>/i', '', $head_content);
            
            // Remove duplicate title tags (WordPress handles this)
            $head_content = preg_replace('/<title[^>]*>.*?<\/title>/is', '', $head_content);
            
            // Clean up extra whitespace
            $head_content = preg_replace('/\s+/', ' ', $head_content);
            $head_content = trim($head_content);
            
            // Output cleaned head content
            if (!empty($head_content)) {
                echo "\n<!-- Landing Page Head Content -->\n";
                echo $head_content;
                echo "\n<!-- End Landing Page Head Content -->\n";
            }
        }
    }

    /**
     * Process and render landing page content
     * UPDATED FOR CLEAN ARCHITECTURE: Check post_content first, then fallback to meta fields
     */
    public static function render_landing_page_content($post) {
        $main_content = '';

        // NEW CLEAN ARCHITECTURE: Check post_content first (user-editable content)
        if (!empty($post->post_content)) {
            $main_content = $post->post_content;
        } else {
            // LEGACY FALLBACK: Try old meta field formats
            $page_data = get_post_meta($post->ID, 'page_data', true);

            if (!empty($page_data) && isset($page_data['main_content'])) {
                $main_content = $page_data['main_content'];
            } else {
                // Fallback to oldest format
                $main_content = get_post_meta($post->ID, 'page_generator_main_content', true);
            }
        }

        if (empty($main_content)) {
            return '<p>No content available.</p>';
        }

        // Process shortcodes in content
        $main_content = do_shortcode($main_content);

        // Apply basic content formatting without problematic filters
        $main_content = wpautop($main_content); // Add paragraphs
        $main_content = wptexturize($main_content); // Smart quotes
        $main_content = convert_smilies($main_content); // Convert smilies

        return $main_content;
    }

    /**
     * Get SEO data with fallbacks
     */
    public static function get_seo_data($post) {
        $seo_data = get_post_meta($post->ID, 'seo_data', true);

        // Provide fallbacks if SEO data is missing
        if (empty($seo_data)) {
            $seo_data = array();
        }

        // Title fallback - try Yoast first, then post title
        if (empty($seo_data['title'])) {
            $yoast_title = get_post_meta($post->ID, '_yoast_wpseo_title', true);
            $seo_data['title'] = !empty($yoast_title) ? $yoast_title : get_the_title($post->ID);
        }

        // Meta description fallback - try Yoast first
        if (empty($seo_data['meta_description'])) {
            $yoast_desc = get_post_meta($post->ID, '_yoast_wpseo_metadesc', true);
            if (!empty($yoast_desc)) {
                $seo_data['meta_description'] = $yoast_desc;
            } else {
                $excerpt = get_the_excerpt($post);
                if (!empty($excerpt)) {
                    $seo_data['meta_description'] = wp_trim_words($excerpt, 25, '...');
                } else {
                    $seo_data['meta_description'] = wp_trim_words(strip_tags($post->post_content), 25, '...');
                }
            }
        }

        // Focus keyphrase fallback - try Yoast first
        if (empty($seo_data['focus_keyphrase'])) {
            $yoast_kw = get_post_meta($post->ID, '_yoast_wpseo_focuskw', true);
            $seo_data['focus_keyphrase'] = !empty($yoast_kw) ? $yoast_kw : get_the_title($post->ID);
        }

        return $seo_data;
    }

    /**
     * Generate breadcrumb schema
     */
    public static function get_breadcrumb_schema($post) {
        $breadcrumbs = array(
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => array(
                array(
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => get_bloginfo('name'),
                    'item' => home_url()
                ),
                array(
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => get_the_title($post->ID),
                    'item' => get_permalink($post->ID)
                )
            )
        );
        
        return $breadcrumbs;
    }

    /**
     * Optimize images for SEO
     */
    public static function optimize_content_images($content, $focus_keyphrase = '') {
        if (empty($content) || empty($focus_keyphrase)) {
            return $content;
        }

        // Add alt text to images that don't have it
        $content = preg_replace_callback(
            '/<img([^>]*?)(?:\s+alt=["\'][^"\']*["\'])?([^>]*?)>/i',
            function($matches) use ($focus_keyphrase) {
                $before = $matches[1];
                $after = $matches[2];
                
                // Check if alt attribute already exists
                if (strpos($before . $after, 'alt=') !== false) {
                    return $matches[0]; // Return original if alt already exists
                }
                
                // Add alt text with focus keyphrase
                $alt_text = esc_attr($focus_keyphrase);
                return '<img' . $before . ' alt="' . $alt_text . '"' . $after . '>';
            },
            $content
        );

        return $content;
    }

    /**
     * Add loading="lazy" to images for performance
     */
    public static function add_lazy_loading($content) {
        if (empty($content)) {
            return $content;
        }

        // Add loading="lazy" to images that don't have it
        $content = preg_replace_callback(
            '/<img([^>]*?)(?:\s+loading=["\'][^"\']*["\'])?([^>]*?)>/i',
            function($matches) {
                $before = $matches[1];
                $after = $matches[2];

                // Check if loading attribute already exists
                if (strpos($before . $after, 'loading=') !== false) {
                    return $matches[0]; // Return original if loading already exists
                }

                // Add loading="lazy"
                return '<img' . $before . ' loading="lazy"' . $after . '>';
            },
            $content
        );

        return $content;
    }

    /**
     * Clean and optimize HTML output
     */
    public static function clean_html_output($content) {
        if (empty($content)) {
            return $content;
        }

        // Remove extra whitespace between tags
        $content = preg_replace('/>\s+</', '><', $content);

        // Remove empty paragraphs
        $content = preg_replace('/<p[^>]*>[\s&nbsp;]*<\/p>/i', '', $content);

        // Clean up multiple consecutive line breaks
        $content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);

        return trim($content);
    }
}

// Initialize the template handler
new PageGenerator_Template_Handler();
