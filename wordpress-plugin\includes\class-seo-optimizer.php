<?php
/**
 * SEO Optimizer Class
 * 
 * Handles all SEO-related functionality for landing pages
 * including meta tags, Open Graph, structured data, and Yoast integration
 */

if (!defined('ABSPATH')) {
    exit;
}

class PageGenerator_SEO_Optimizer {
    
    public function __construct() {
        // Only add SEO optimization if Yoast SEO is not active
        if (!$this->is_yoast_seo_active()) {
            add_action('wp_head', array($this, 'optimize_head_output'), 1);
            add_filter('document_title_parts', array($this, 'optimize_title'), 10, 1);
            // Only add structured data if Yoast is not active
            add_action('wp_head', array($this, 'add_structured_data'), 15);
        }
    }

    /**
     * Optimize head output for SEO
     * Remove duplicates and organize tags properly
     */
    public function optimize_head_output() {
        if (!is_singular('landing_page')) {
            return;
        }

        global $post;
        $seo_data = get_post_meta($post->ID, 'seo_data', true);
        
        if (!empty($seo_data)) {
            $this->output_optimized_meta_tags($seo_data, $post);
        }
    }

    /**
     * Output optimized meta tags in correct order
     */
    private function output_optimized_meta_tags($seo_data, $post) {
        // Basic SEO meta tags
        if (!empty($seo_data['meta_description'])) {
            echo '<meta name="description" content="' . esc_attr($seo_data['meta_description']) . '">' . "\n";
        }

        if (!empty($seo_data['focus_keyphrase'])) {
            echo '<meta name="keywords" content="' . esc_attr($seo_data['focus_keyphrase']) . '">' . "\n";
        }

        // Canonical URL
        echo '<link rel="canonical" href="' . esc_url(get_permalink($post->ID)) . '">' . "\n";

        // Robots meta
        echo '<meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">' . "\n";

        // Open Graph tags
        $this->output_open_graph_tags($seo_data, $post);

        // Twitter Card tags
        $this->output_twitter_card_tags($seo_data, $post);
    }

    /**
     * Output Open Graph meta tags
     */
    private function output_open_graph_tags($seo_data, $post) {
        $title = !empty($seo_data['title']) ? $seo_data['title'] : get_the_title($post->ID);
        $description = !empty($seo_data['meta_description']) ? $seo_data['meta_description'] : '';
        $url = get_permalink($post->ID);
        $site_name = get_bloginfo('name');

        echo '<meta property="og:type" content="website">' . "\n";
        echo '<meta property="og:title" content="' . esc_attr($title) . '">' . "\n";
        echo '<meta property="og:description" content="' . esc_attr($description) . '">' . "\n";
        echo '<meta property="og:url" content="' . esc_url($url) . '">' . "\n";
        echo '<meta property="og:site_name" content="' . esc_attr($site_name) . '">' . "\n";
        echo '<meta property="og:locale" content="' . esc_attr(get_locale()) . '">' . "\n";

        // Featured image for Open Graph
        $featured_image = get_the_post_thumbnail_url($post->ID, 'large');
        if ($featured_image) {
            echo '<meta property="og:image" content="' . esc_url($featured_image) . '">' . "\n";
            echo '<meta property="og:image:width" content="1200">' . "\n";
            echo '<meta property="og:image:height" content="630">' . "\n";
        }
    }

    /**
     * Output Twitter Card meta tags
     */
    private function output_twitter_card_tags($seo_data, $post) {
        $title = !empty($seo_data['title']) ? $seo_data['title'] : get_the_title($post->ID);
        $description = !empty($seo_data['meta_description']) ? $seo_data['meta_description'] : '';

        echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
        echo '<meta name="twitter:title" content="' . esc_attr($title) . '">' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr($description) . '">' . "\n";

        $featured_image = get_the_post_thumbnail_url($post->ID, 'large');
        if ($featured_image) {
            echo '<meta name="twitter:image" content="' . esc_url($featured_image) . '">' . "\n";
        }
    }

    /**
     * Add structured data (JSON-LD)
     */
    public function add_structured_data() {
        if (!is_singular('landing_page')) {
            return;
        }

        global $post;
        $seo_data = get_post_meta($post->ID, 'seo_data', true);
        
        $structured_data = array(
            '@context' => 'https://schema.org',
            '@type' => 'WebPage',
            'name' => !empty($seo_data['title']) ? $seo_data['title'] : get_the_title($post->ID),
            'description' => !empty($seo_data['meta_description']) ? $seo_data['meta_description'] : '',
            'url' => get_permalink($post->ID),
            'inLanguage' => get_locale(),
            'isPartOf' => array(
                '@type' => 'WebSite',
                'name' => get_bloginfo('name'),
                'url' => home_url()
            )
        );

        // Add featured image to structured data
        $featured_image = get_the_post_thumbnail_url($post->ID, 'large');
        if ($featured_image) {
            $structured_data['image'] = array(
                '@type' => 'ImageObject',
                'url' => $featured_image,
                'width' => 1200,
                'height' => 630
            );
        }

        echo '<script type="application/ld+json">' . "\n";
        echo wp_json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        echo "\n" . '</script>' . "\n";
    }

    /**
     * Optimize page title
     */
    public function optimize_title($title_parts) {
        if (!is_singular('landing_page')) {
            return $title_parts;
        }

        global $post;
        $seo_data = get_post_meta($post->ID, 'seo_data', true);
        
        if (!empty($seo_data['title'])) {
            $title_parts['title'] = $seo_data['title'];
        }

        return $title_parts;
    }

    /**
     * Generate SEO-friendly slug
     */
    public static function generate_seo_slug($title, $max_length = 35) {
        // Indonesian and English stop words
        $stop_words = array(
            'dan', 'atau', 'untuk', 'dengan', 'yang', 'di', 'ke', 'dari', 'pada', 'dalam', 
            'adalah', 'akan', 'telah', 'sudah', 'juga', 'dapat', 'bisa', 'harus', 'tidak', 
            'belum', 'masih', 'sangat', 'lebih', 'paling', 'saja', 'hanya', 'sekali', 'kali', 
            'banyak', 'sedikit', 'jasa', 'layanan', 'terbaik', 'terpercaya', 'profesional',
            'the', 'and', 'or', 'for', 'with', 'that', 'in', 'to', 'from', 'on', 'at', 
            'is', 'are', 'was', 'were', 'will', 'have', 'has', 'had', 'can', 'could', 
            'should', 'would', 'may', 'might', 'must', 'shall', 'do', 'does', 'did', 
            'a', 'an', 'this', 'that', 'these', 'those', 'best', 'professional', 'service', 'services'
        );

        // Convert to lowercase and remove special characters
        $slug = strtolower($title);
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        
        // Split into words
        $words = explode(' ', $slug);
        $filtered_words = array();
        
        // Remove stop words but keep important keywords
        foreach ($words as $word) {
            $word = trim($word);
            if (!empty($word) && !in_array($word, $stop_words)) {
                $filtered_words[] = $word;
            }
        }
        
        // If all words were filtered out, use original words
        if (empty($filtered_words)) {
            $filtered_words = array_slice($words, 0, 5);
        }
        
        // Join words and limit length
        $slug = implode('-', $filtered_words);
        
        if (strlen($slug) > $max_length) {
            $slug = substr($slug, 0, $max_length);
            // Ensure we don't cut in the middle of a word
            $last_dash = strrpos($slug, '-');
            if ($last_dash !== false && $last_dash > $max_length * 0.7) {
                $slug = substr($slug, 0, $last_dash);
            }
        }
        
        return sanitize_title($slug);
    }

    /**
     * Check if Yoast SEO plugin is active
     */
    private function is_yoast_seo_active() {
        // Check multiple ways to ensure Yoast SEO is detected
        return defined('WPSEO_VERSION') ||
               class_exists('WPSEO_Options') ||
               is_plugin_active('wordpress-seo/wp-seo.php') ||
               function_exists('wpseo_init');
    }

    /**
     * Update Yoast SEO meta fields
     */
    public static function update_yoast_meta($post_id, $seo_data) {
        if (!empty($seo_data['title'])) {
            update_post_meta($post_id, '_yoast_wpseo_title', $seo_data['title']);
        }

        if (!empty($seo_data['meta_description'])) {
            update_post_meta($post_id, '_yoast_wpseo_metadesc', $seo_data['meta_description']);
        }

        if (!empty($seo_data['focus_keyphrase'])) {
            update_post_meta($post_id, '_yoast_wpseo_focuskw', $seo_data['focus_keyphrase']);
        }

        // Set SEO score to good if we have all required fields
        if (!empty($seo_data['title']) && !empty($seo_data['meta_description']) && !empty($seo_data['focus_keyphrase'])) {
            update_post_meta($post_id, '_yoast_wpseo_linkdex', 75); // Good SEO score
        }
    }
}

// Initialize the SEO optimizer
new PageGenerator_SEO_Optimizer();
