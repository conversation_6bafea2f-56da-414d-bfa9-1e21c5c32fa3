<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Template;

class UpdateTemplatePricingSectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update all existing templates to have pricing section as static
        $templates = Template::all();
        
        foreach ($templates as $template) {
            $sectionSettings = $template->getSectionSettings();
            
            // Update pricing section to be static
            if (isset($sectionSettings['pricing'])) {
                $sectionSettings['pricing']['type'] = 'static';
                $template->section_settings = $sectionSettings;
                $template->save();
                
                $this->command->info("Updated template '{$template->name}' - pricing section set to static");
            }
        }
        
        $this->command->info('All templates updated successfully!');
    }
}
