@extends('layouts.app')

@section('title', $template->name)
@section('page-title', $template->name)

@section('content')
<div class="min-h-screen bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white">{{ $template->name }}</h1>
                    <p class="text-gray-400 mt-1">{{ $template->description ?: 'Tidak ada deskripsi' }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    @if($template->is_processed)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-800 text-green-100">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Processed
                        </span>
                    @else
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-800 text-yellow-100">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Draft
                        </span>
                    @endif
                    
                    <a href="{{ route('templates.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-white">Actions</h2>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('templates.preview', $template) }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        Preview
                    </a>
                    
                    <a href="{{ route('templates.edit', $template) }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Template
                    </a>
                    
                    <button id="process-btn"
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white {{ $template->is_processed ? 'bg-orange-600 hover:bg-orange-700' : 'bg-green-600 hover:bg-green-700' }}">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        {{ $template->is_processed ? 'Re-Proses Template' : 'Proses Template' }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Template Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Sections</p>
                        <p class="text-2xl font-semibold text-white">{{ count($template->sections ?? []) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a.997.997 0 01-1.414 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Placeholders</p>
                        <p class="text-2xl font-semibold text-white">{{ count($template->placeholders ?? []) }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Generated Pages</p>
                        <p class="text-2xl font-semibold text-white">{{ $template->generatedPages->count() }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400">Status</p>
                        <p class="text-2xl font-semibold text-white">{{ $template->is_active ? 'Active' : 'Inactive' }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sections -->
        @if($template->sections)
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-lg font-semibold text-white mb-4">Sections</h2>
                <div class="space-y-4">
                    @foreach(collect($template->sections)->sortBy('order') as $section)
                        <div class="border border-gray-600 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-md font-medium text-white">{{ $section['name'] }}</h3>
                                <span class="text-sm text-gray-400">Order: {{ $section['order'] }}</span>
                            </div>
                            <div class="bg-gray-900 rounded p-3">
                                <pre class="text-sm text-gray-300 whitespace-pre-wrap">{{ Str::limit($section['html_content'], 200) }}</pre>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Placeholders -->
        @if($template->is_processed && $template->placeholders)
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-lg font-semibold text-white mb-4">Placeholders</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @foreach($template->placeholders as $placeholder => $originalContent)
                        <div class="border border-gray-600 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-2">
                                <code class="text-sm text-cyan-400">{{ $placeholder }}</code>
                            </div>
                            <p class="text-sm text-gray-300">{{ $originalContent }}</p>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Master Prompt -->
        @if($template->master_prompt)
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-lg font-semibold text-white mb-4">Master Prompt</h2>
                <div class="bg-gray-900 rounded p-4">
                    <pre class="text-sm text-gray-300 whitespace-pre-wrap">{{ $template->master_prompt }}</pre>
                </div>
            </div>
        @endif

        <!-- Additional Info -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            @if($template->styles)
                <div class="bg-gray-800 rounded-lg shadow-lg p-6">
                    <h3 class="text-md font-semibold text-white mb-3">Custom CSS</h3>
                    <div class="bg-gray-900 rounded p-3">
                        <pre class="text-sm text-gray-300 whitespace-pre-wrap">{{ Str::limit($template->styles, 150) }}</pre>
                    </div>
                </div>
            @endif

            @if($template->scripts)
                <div class="bg-gray-800 rounded-lg shadow-lg p-6">
                    <h3 class="text-md font-semibold text-white mb-3">Scripts</h3>
                    <div class="bg-gray-900 rounded p-3">
                        <pre class="text-sm text-gray-300 whitespace-pre-wrap">{{ Str::limit($template->scripts, 150) }}</pre>
                    </div>
                </div>
            @endif

            @if($template->fonts)
                <div class="bg-gray-800 rounded-lg shadow-lg p-6">
                    <h3 class="text-md font-semibold text-white mb-3">Fonts</h3>
                    <div class="bg-gray-900 rounded p-3">
                        <pre class="text-sm text-gray-300 whitespace-pre-wrap">{{ Str::limit($template->fonts, 150) }}</pre>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const processBtn = document.getElementById('process-btn');
    
    if (processBtn) {
        processBtn.addEventListener('click', function() {
            const isReprocess = processBtn.textContent.includes('Re-Proses');
            const confirmMessage = isReprocess
                ? 'Re-proses template akan memperbarui placeholder dan master prompt. Placeholder lama akan diganti. Lanjutkan?'
                : 'Proses template akan menghasilkan placeholder otomatis dari tag P dan H1-H6. Lanjutkan?';

            if (confirm(confirmMessage)) {
                processBtn.disabled = true;
                processBtn.innerHTML = `
                    <svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                `;

                fetch(`{{ route('templates.process', $template) }}`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Template berhasil diproses! ${data.placeholders_count} placeholder ditemukan.`);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                        processBtn.disabled = false;
                        processBtn.innerHTML = `
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Proses Template
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Terjadi kesalahan saat memproses template');
                    processBtn.disabled = false;
                    processBtn.innerHTML = `
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Proses Template
                    `;
                });
            }
        });
    }
});
</script>
@endsection
