@extends('layouts.app')

@section('title', 'Master Prompt - ' . $campaign->name)
@section('page-title', 'Master Prompt Generator')

@section('content')
<div class="space-y-6">
    <!-- Campaign Info -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium text-white">{{ $campaign->name }}</h3>
                    <p class="text-sm text-gray-400">
                        Business: {{ $campaign->business_name }} • Keyword: {{ $campaign->main_keyword }}
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                        @if($campaign->status === 'active') bg-green-800 text-green-100
                        @elseif($campaign->status === 'draft') bg-yellow-800 text-yellow-100
                        @else bg-gray-800 text-gray-100 @endif">
                        {{ ucfirst($campaign->status) }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Master Prompt Display -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg leading-6 font-medium text-white">Generated Master Prompt</h3>
                <div class="flex space-x-2">
                    <button onclick="copyPrompt()" 
                            class="inline-flex items-center px-3 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Copy Prompt
                    </button>
                    <button onclick="regeneratePrompt()" 
                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Regenerate
                    </button>
                </div>
            </div>
            
            <div class="bg-gray-900 rounded-lg p-4 border border-gray-700">
                <pre id="master-prompt" class="text-sm text-gray-300 whitespace-pre-wrap font-mono overflow-x-auto">{{ $masterPrompt }}</pre>
            </div>

            <div class="mt-4 text-xs text-gray-500">
                <p>💡 <strong>Tip:</strong> Copy this prompt and use it with your preferred AI tool (ChatGPT, Claude, etc.) to generate content for your landing pages.</p>
            </div>
        </div>
    </div>

    <!-- Campaign Data Preview -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-white mb-4">Campaign Data Used</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <dt class="text-sm font-medium text-gray-400">Business Name</dt>
                    <dd class="mt-1 text-sm text-white">{{ $campaign->business_name }}</dd>
                </div>
                
                <div>
                    <dt class="text-sm font-medium text-gray-400">Main Keyword</dt>
                    <dd class="mt-1 text-sm text-white">{{ $campaign->main_keyword }}</dd>
                </div>
                
                <div class="md:col-span-2">
                    <dt class="text-sm font-medium text-gray-400">Business Description</dt>
                    <dd class="mt-1 text-sm text-white">{{ $campaign->business_description }}</dd>
                </div>
                
                @if($campaign->contact_phone)
                    <div>
                        <dt class="text-sm font-medium text-gray-400">Contact Phone</dt>
                        <dd class="mt-1 text-sm text-white">{{ $campaign->contact_phone }}</dd>
                    </div>
                @endif
                
                @if($campaign->contact_email)
                    <div>
                        <dt class="text-sm font-medium text-gray-400">Contact Email</dt>
                        <dd class="mt-1 text-sm text-white">{{ $campaign->contact_email }}</dd>
                    </div>
                @endif
                
                @if($campaign->address)
                    <div class="md:col-span-2">
                        <dt class="text-sm font-medium text-gray-400">Address</dt>
                        <dd class="mt-1 text-sm text-white">{{ $campaign->address }}</dd>
                    </div>
                @endif
                
                @if($campaign->people_also_ask && count($campaign->people_also_ask) > 0)
                    <div class="md:col-span-2">
                        <dt class="text-sm font-medium text-gray-400">People Also Ask Questions</dt>
                        <dd class="mt-1">
                            <ul class="list-disc list-inside text-sm text-white space-y-1">
                                @foreach($campaign->people_also_ask as $question)
                                    <li>{{ $question }}</li>
                                @endforeach
                            </ul>
                        </dd>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="bg-blue-900/20 border border-blue-800 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-300">How to use this Master Prompt</h3>
                <div class="mt-2 text-sm text-blue-200">
                    <ol class="list-decimal list-inside space-y-1">
                        <li>Copy the generated master prompt above</li>
                        <li>Use it with your preferred AI tool (ChatGPT, Claude, Gemini, etc.)</li>
                        <li>The AI will generate JSON data with unique content variations</li>
                        <li>Upload the JSON result to our Content Generator</li>
                        <li>Generate multiple landing pages automatically</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-between">
        <a href="{{ route('campaigns.show', $campaign) }}" 
           class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Campaign
        </a>
        
        <div class="flex space-x-3">
            <a href="{{ route('content-generator.upload', $campaign) }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                Upload JSON Content
            </a>
        </div>
    </div>
</div>

@push('scripts')
<script>
function copyPrompt() {
    const promptElement = document.getElementById('master-prompt');
    const textArea = document.createElement('textarea');
    textArea.value = promptElement.textContent;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    
    // Show success message
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    button.innerHTML = `
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        Copied!
    `;
    button.classList.remove('bg-gray-700', 'hover:bg-gray-600');
    button.classList.add('bg-green-600', 'hover:bg-green-700');
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('bg-green-600', 'hover:bg-green-700');
        button.classList.add('bg-gray-700', 'hover:bg-gray-600');
    }, 2000);
}

function regeneratePrompt() {
    const button = event.target.closest('button');
    const originalText = button.innerHTML;
    
    button.innerHTML = `
        <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Regenerating...
    `;
    button.disabled = true;
    
    fetch(`{{ route('campaigns.master-prompt', $campaign) }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('master-prompt').textContent = data.prompt;
        } else {
            alert('Error regenerating prompt: ' + data.error);
        }
    })
    .catch(error => {
        alert('Network error: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}
</script>
@endpush
@endsection
