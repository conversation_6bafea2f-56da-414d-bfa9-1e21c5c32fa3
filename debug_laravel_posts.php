<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

echo 'MENCARI POSTS YANG DI-EXPORT DARI LARAVEL:' . PHP_EOL;
$posts = get_posts(array(
    'post_type' => 'landing_page',
    'numberposts' => 10,
    'meta_query' => array(
        'relation' => 'OR',
        array(
            'key' => 'page_data',
            'compare' => 'EXISTS'
        ),
        array(
            'key' => 'page_generator_source',
            'compare' => 'EXISTS'
        )
    )
));

foreach ($posts as $post) {
    echo 'ID: ' . $post->ID . ' | Title: ' . $post->post_title . ' | Slug: ' . $post->post_name . PHP_EOL;
    
    $page_data = get_post_meta($post->ID, 'page_data', true);
    $source = get_post_meta($post->ID, 'page_generator_source', true);
    
    if ($page_data) {
        echo '  - Has page_data (NEW FORMAT)' . PHP_EOL;
        if (isset($page_data['main_content'])) {
            echo '  - main_content length: ' . strlen($page_data['main_content']) . PHP_EOL;
        }
    }
    
    if ($source) {
        echo '  - Source: ' . $source . PHP_EOL;
    }
    
    echo PHP_EOL;
}

// Jika tidak ada, cari semua landing pages
if (empty($posts)) {
    echo 'Tidak ada posts dari Laravel, mencari semua landing pages:' . PHP_EOL;
    $all_posts = get_posts(array(
        'post_type' => 'landing_page',
        'numberposts' => 10
    ));
    
    foreach ($all_posts as $post) {
        echo 'ID: ' . $post->ID . ' | Title: ' . $post->post_title . PHP_EOL;
    }
}
?>
