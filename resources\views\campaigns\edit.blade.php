@extends('layouts.app')

@section('title', 'Edit Campaign - ' . $campaign->name)
@section('page-title', 'Edit Campaign')

@section('content')
<div class="space-y-6">
    <!-- Campaign Form -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form action="{{ route('campaigns.update', $campaign) }}" method="POST" class="space-y-6">
                @csrf
                @method('PUT')
                
                <!-- Campaign Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-300">Campaign Name</label>
                    <input type="text" name="name" id="name" value="{{ old('name', $campaign->name) }}" 
                           class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm" 
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Business Name -->
                <div>
                    <label for="business_name" class="block text-sm font-medium text-gray-300">Business Name</label>
                    <input type="text" name="business_name" id="business_name" value="{{ old('business_name', $campaign->business_name) }}" 
                           class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm" 
                           required>
                    @error('business_name')
                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Main Keyword -->
                <div>
                    <label for="main_keyword" class="block text-sm font-medium text-gray-300">Main Keyword</label>
                    <input type="text" name="main_keyword" id="main_keyword" value="{{ old('main_keyword', $campaign->main_keyword) }}" 
                           class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm" 
                           required>
                    @error('main_keyword')
                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Business Description -->
                <div>
                    <label for="business_description" class="block text-sm font-medium text-gray-300">Business Description</label>
                    <textarea name="business_description" id="business_description" rows="4" 
                              class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm" 
                              required>{{ old('business_description', $campaign->business_description) }}</textarea>
                    @error('business_description')
                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Contact Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="contact_phone" class="block text-sm font-medium text-gray-300">Contact Phone</label>
                        <input type="text" name="contact_phone" id="contact_phone" value="{{ old('contact_phone', $campaign->contact_phone) }}" 
                               class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm">
                        @error('contact_phone')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="contact_email" class="block text-sm font-medium text-gray-300">Contact Email</label>
                        <input type="email" name="contact_email" id="contact_email" value="{{ old('contact_email', $campaign->contact_email) }}" 
                               class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm">
                        @error('contact_email')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Address -->
                <div>
                    <label for="address" class="block text-sm font-medium text-gray-300">Address</label>
                    <textarea name="address" id="address" rows="3"
                              class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm">{{ old('address', $campaign->address) }}</textarea>
                    @error('address')
                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- WordPress Connection -->
                <div>
                    <label for="wordpress_connection_id" class="block text-sm font-medium text-gray-300">WordPress Connection</label>
                    <p class="text-sm text-gray-400 mb-2">Select which WordPress site to use for exporting pages from this campaign.</p>
                    <select name="wordpress_connection_id" id="wordpress_connection_id"
                            class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                        <option value="">Select WordPress Connection (Optional)</option>
                        @foreach($wordpressConnections as $connection)
                            <option value="{{ $connection->id }}"
                                    {{ old('wordpress_connection_id', $campaign->wordpress_connection_id) == $connection->id ? 'selected' : '' }}>
                                {{ $connection->name }} - {{ $connection->url }}
                                @if($connection->is_default) (Default) @endif
                            </option>
                        @endforeach
                    </select>
                    @error('wordpress_connection_id')
                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                    @if($wordpressConnections->isEmpty())
                        <p class="mt-1 text-sm text-yellow-400">
                            No WordPress connections available.
                            <a href="{{ route('wordpress.connections.create') }}" class="text-cyan-400 hover:text-cyan-300">Create one here</a>.
                        </p>
                    @endif
                </div>

                <!-- Template Selection -->
                <div>
                    <label for="template_id" class="block text-sm font-medium text-gray-300">Landing Page Template</label>
                    <p class="text-sm text-gray-400 mb-2">Select a template for generating landing pages from this campaign.</p>
                    <select name="template_id" id="template_id"
                            class="mt-1 block w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-cyan-500 focus:border-cyan-500">
                        <option value="">Select Template (Optional)</option>
                        @foreach($templates as $template)
                            <option value="{{ $template->id }}"
                                    {{ old('template_id', $campaign->template_id) == $template->id ? 'selected' : '' }}>
                                {{ $template->name }}
                                @if($template->is_processed)
                                    ({{ count($template->placeholders ?? []) }} placeholders)
                                @else
                                    (Not processed yet)
                                @endif
                            </option>
                        @endforeach
                    </select>
                    @error('template_id')
                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                    @if($templates->isEmpty())
                        <p class="mt-1 text-sm text-yellow-400">
                            No templates available.
                            <a href="{{ route('templates.create') }}" class="text-cyan-400 hover:text-cyan-300">Create one here</a>.
                        </p>
                    @endif
                </div>

                <!-- People Also Ask -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">People Also Ask Questions</label>
                    <div id="faq-container" class="space-y-2">
                        @if(old('people_also_ask', $campaign->people_also_ask))
                            @foreach(old('people_also_ask', $campaign->people_also_ask) as $index => $question)
                                <div class="flex items-center space-x-2 faq-item">
                                    <input type="text" name="people_also_ask[]" value="{{ $question }}" 
                                           class="flex-1 rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm" 
                                           placeholder="Enter FAQ question">
                                    <button type="button" onclick="removeFaqItem(this)" 
                                            class="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="flex items-center space-x-2 faq-item">
                                <input type="text" name="people_also_ask[]" 
                                       class="flex-1 rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm" 
                                       placeholder="Enter FAQ question">
                                <button type="button" onclick="removeFaqItem(this)" 
                                        class="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif
                    </div>
                    <button type="button" onclick="addFaqItem()" 
                            class="mt-2 inline-flex items-center px-3 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add FAQ Question
                    </button>
                    @error('people_also_ask')
                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-300">Status</label>
                    <select name="status" id="status" 
                            class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm">
                        <option value="draft" {{ old('status', $campaign->status) === 'draft' ? 'selected' : '' }}>Draft</option>
                        <option value="active" {{ old('status', $campaign->status) === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="completed" {{ old('status', $campaign->status) === 'completed' ? 'selected' : '' }}>Completed</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Form Actions -->
                <div class="flex justify-between">
                    <a href="{{ route('campaigns.show', $campaign) }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Cancel
                    </a>
                    
                    <button type="submit" 
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Update Campaign
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function addFaqItem() {
    const container = document.getElementById('faq-container');
    const newItem = document.createElement('div');
    newItem.className = 'flex items-center space-x-2 faq-item';
    newItem.innerHTML = `
        <input type="text" name="people_also_ask[]" 
               class="flex-1 rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-cyan-500 focus:ring-cyan-500 sm:text-sm" 
               placeholder="Enter FAQ question">
        <button type="button" onclick="removeFaqItem(this)" 
                class="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;
    container.appendChild(newItem);
}

function removeFaqItem(button) {
    const container = document.getElementById('faq-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}
</script>
@endpush
@endsection
