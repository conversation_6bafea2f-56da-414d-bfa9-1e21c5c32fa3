@extends('layouts.app')

@section('title', 'Upload JSON - ' . $campaign->name)
@section('page-title', 'Upload JSON & Generate Pages')

@section('content')
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Campaign Info -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-cyan-500 rounded-lg flex items-center justify-center mr-4">
                        <span class="text-sm font-medium text-white">{{ substr($campaign->name, 0, 2) }}</span>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-white">{{ $campaign->name }}</h3>
                        <p class="text-sm text-gray-400">{{ $campaign->business_name }} • {{ $campaign->main_keyword }}</p>
                    </div>
                </div>
                <button id="masterPromptBtn" onclick="showMasterPrompt()"
                        class="inline-flex items-center px-3 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                    <span id="masterPromptBtnText">Select Template First</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="bg-blue-900 border border-blue-700 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-100">Instructions</h3>
                <div class="mt-2 text-sm text-blue-200">
                    <ol class="list-decimal list-inside space-y-1">
                        <li>Copy the master prompt by clicking "View Master Prompt" above</li>
                        <li>Paste it into your AI tool (ChatGPT, Claude, etc.)</li>
                        <li>Copy the JSON response from the AI</li>
                        <li>Save it as a .json file and upload it below</li>
                        <li>Select a template and click "Generate Pages"</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Form -->
    <form action="{{ route('content-generator.process', $campaign) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
        @csrf
        
        <!-- Template Selection -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">Select Template</h3>
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    @foreach($templates as $template)
                        <label class="relative cursor-pointer">
                            <input type="radio" name="template_id" value="{{ $template->id }}" required
                                   class="sr-only peer template-radio" onchange="onTemplateChange()">
                            <div class="bg-gray-700 border-2 border-gray-600 rounded-lg p-4 peer-checked:border-cyan-500 peer-checked:bg-gray-600 hover:bg-gray-600 transition-colors">
                                @if($template->preview_image)
                                    <img src="{{ Storage::url($template->preview_image) }}" 
                                         alt="{{ $template->name }}" 
                                         class="w-full h-32 object-cover rounded-md mb-3">
                                @else
                                    <div class="w-full h-32 bg-gray-600 rounded-md mb-3 flex items-center justify-center">
                                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                                        </svg>
                                    </div>
                                @endif
                                <h4 class="text-sm font-medium text-white">{{ $template->name }}</h4>
                                @if($template->description)
                                    <p class="text-xs text-gray-400 mt-1">{{ Str::limit($template->description, 80) }}</p>
                                @endif
                                <div class="mt-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-600 text-gray-300">
                                        {{ is_array($template->placeholders) ? count($template->placeholders) : (is_string($template->placeholders) ? count(json_decode($template->placeholders, true) ?: []) : 0) }} placeholders
                                    </span>
                                </div>
                            </div>
                        </label>
                    @endforeach
                </div>
                @error('template_id')
                    <p class="mt-2 text-sm text-red-400">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- JSON File Upload -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">Upload JSON File</h3>
                
                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-600 border-dashed rounded-md hover:border-gray-500 transition-colors">
                    <div class="space-y-1 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="flex text-sm text-gray-400">
                            <label for="json_file" class="relative cursor-pointer bg-gray-800 rounded-md font-medium text-cyan-400 hover:text-cyan-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-cyan-500">
                                <span>Upload a JSON file</span>
                                <input id="json_file" name="json_file" type="file" accept=".json,.txt" required class="sr-only">
                            </label>
                            <p class="pl-1">or drag and drop</p>
                        </div>
                        <p class="text-xs text-gray-400">JSON or TXT files only</p>
                    </div>
                </div>
                
                @error('json_file')
                    <p class="mt-2 text-sm text-red-400">{{ $message }}</p>
                @enderror
                
                <!-- File Preview -->
                <div id="filePreview" class="mt-4 hidden">
                    <div class="bg-gray-700 rounded-md p-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-cyan-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span id="fileName" class="text-sm text-white"></span>
                            </div>
                            <button type="button" onclick="removeFile()" class="text-red-400 hover:text-red-300">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3">
            <a href="{{ route('content-generator.index') }}" 
               class="inline-flex items-center px-4 py-2 border border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                Cancel
            </a>
            <button type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Generate Pages
            </button>
        </div>
    </form>
</div>

<!-- Master Prompt Modal -->
<div id="promptModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden z-50" onclick="closeModal()">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="inline-block align-bottom bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6" onclick="event.stopPropagation()">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg leading-6 font-medium text-white">Master Prompt for {{ $campaign->name }}</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="mb-4">
                <p class="text-sm text-gray-400 mb-3">Copy this prompt and paste it into your AI tool:</p>
                <div class="relative">
                    <textarea id="promptText" readonly 
                              class="w-full h-64 bg-gray-700 border border-gray-600 rounded-md p-3 text-white text-sm font-mono resize-none focus:outline-none focus:ring-cyan-500 focus:border-cyan-500"></textarea>
                    <button onclick="copyPrompt()" 
                            class="absolute top-2 right-2 px-3 py-1 bg-cyan-600 hover:bg-cyan-700 text-white text-xs rounded-md">
                        Copy
                    </button>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button onclick="closeModal()" 
                        class="px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let selectedTemplateId = null;

function onTemplateChange() {
    const selectedTemplate = document.querySelector('input[name="template_id"]:checked');
    const masterPromptBtn = document.getElementById('masterPromptBtn');
    const masterPromptBtnText = document.getElementById('masterPromptBtnText');

    if (selectedTemplate) {
        selectedTemplateId = selectedTemplate.value;
        masterPromptBtn.disabled = false;
        masterPromptBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        masterPromptBtn.classList.add('hover:bg-gray-600');
        masterPromptBtnText.textContent = 'View Master Prompt';
    } else {
        selectedTemplateId = null;
        masterPromptBtn.disabled = true;
        masterPromptBtn.classList.add('opacity-50', 'cursor-not-allowed');
        masterPromptBtn.classList.remove('hover:bg-gray-600');
        masterPromptBtnText.textContent = 'Select Template First';
    }
}

function showMasterPrompt() {
    if (!selectedTemplateId) {
        alert('Please select a template first.');
        return;
    }

    const url = `/content-generator/{{ $campaign->id }}/master-prompt?template_id=${selectedTemplateId}`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error: ' + data.error);
                return;
            }

            const promptTextArea = document.getElementById('promptText');
            promptTextArea.value = data.prompt;

            // Make textarea readonly if specified
            if (data.readonly) {
                promptTextArea.readOnly = true;
                promptTextArea.classList.add('bg-gray-600', 'cursor-not-allowed');
            } else {
                promptTextArea.readOnly = false;
                promptTextArea.classList.remove('bg-gray-600', 'cursor-not-allowed');
            }

            document.getElementById('promptModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading master prompt');
        });
}

function closeModal() {
    document.getElementById('promptModal').classList.add('hidden');
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    onTemplateChange(); // Check initial state
});

function copyPrompt() {
    const promptText = document.getElementById('promptText');
    promptText.select();
    document.execCommand('copy');
    
    // Show feedback
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    button.classList.add('bg-green-600');
    button.classList.remove('bg-cyan-600');
    
    setTimeout(() => {
        button.textContent = originalText;
        button.classList.remove('bg-green-600');
        button.classList.add('bg-cyan-600');
    }, 2000);
}

// File upload handling
document.getElementById('json_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('filePreview').classList.remove('hidden');
    }
});

function removeFile() {
    document.getElementById('json_file').value = '';
    document.getElementById('filePreview').classList.add('hidden');
}

// Close modal on Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeModal();
    }
});
</script>
@endpush
@endsection
