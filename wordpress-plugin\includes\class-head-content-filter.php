<?php
/**
 * Head Content Filter Class
 * 
 * Filters and optimizes head content from Lara<PERSON> to prevent duplications
 * with WordPress and Yoast SEO meta tags
 */

if (!defined('ABSPATH')) {
    exit;
}

class PageGenerator_Head_Content_Filter {
    
    /**
     * Filter head content to remove duplications
     */
    public static function filter_head_content($head_content) {
        if (empty($head_content)) {
            return '';
        }
        
        // Remove DOCTYPE if present (WordPress handles this)
        $head_content = preg_replace('/<!DOCTYPE[^>]*>/i', '', $head_content);
        
        // Remove html and head tags if present (we only want the inner content)
        $head_content = preg_replace('/<\/?html[^>]*>/i', '', $head_content);
        $head_content = preg_replace('/<\/?head[^>]*>/i', '', $head_content);
        
        // Remove meta charset (WordPress already adds this)
        $head_content = preg_replace('/<meta\s+charset[^>]*>/i', '', $head_content);
        
        // Remove meta viewport (WordPress already adds this)
        $head_content = preg_replace('/<meta\s+name=["\']viewport["\'][^>]*>/i', '', $head_content);
        
        // Remove title tags (Yoast SEO handles this)
        $head_content = preg_replace('/<title[^>]*>.*?<\/title>/is', '', $head_content);
        
        // Remove meta description (Yoast SEO handles this) - all variations
        $head_content = preg_replace('/<meta\s+[^>]*name=["\']description["\'][^>]*>/i', '', $head_content);
        $head_content = preg_replace('/<meta\s+[^>]*property=["\']description["\'][^>]*>/i', '', $head_content);

        // Remove meta keywords (Yoast SEO handles this)
        $head_content = preg_replace('/<meta\s+[^>]*name=["\']keywords["\'][^>]*>/i', '', $head_content);

        // Remove meta tags containing Yoast placeholders that weren't replaced
        $head_content = preg_replace('/<meta\s+[^>]*content=["\'][^"\']*\{\{Yoast_[^}]*\}\}[^"\']*["\'][^>]*>/i', '', $head_content);

        // Remove any remaining Yoast placeholders that weren't replaced
        $head_content = preg_replace('/\{\{Yoast_[^}]*\}\}/', '', $head_content);

        // Remove empty meta tags that might result from placeholder removal
        $head_content = preg_replace('/<meta\s+[^>]*content=["\']["\'][^>]*>/i', '', $head_content);
        $head_content = preg_replace('/<meta\s+[^>]*content=["\'][\s]*["\'][^>]*>/i', '', $head_content);
        
        // Remove canonical links (Yoast SEO handles this) - all variations
        $head_content = preg_replace('/<link\s+[^>]*rel=["\']canonical["\'][^>]*>/i', '', $head_content);
        $head_content = preg_replace('/<link\s+[^>]*href=["\'][^"\']*["\'][^>]*rel=["\']canonical["\'][^>]*>/i', '', $head_content);
        
        // Remove robots meta (Yoast SEO handles this)
        $head_content = preg_replace('/<meta\s+name=["\']robots["\'][^>]*>/i', '', $head_content);
        
        // Remove Open Graph tags (Yoast SEO handles this)
        $head_content = preg_replace('/<meta\s+property=["\']og:[^"\']*["\'][^>]*>/i', '', $head_content);
        
        // Remove Twitter Card tags (Yoast SEO handles this)
        $head_content = preg_replace('/<meta\s+name=["\']twitter:[^"\']*["\'][^>]*>/i', '', $head_content);
        
        // Clean up extra whitespace and empty lines
        $head_content = preg_replace('/\s+/', ' ', $head_content);
        $head_content = preg_replace('/>\s+</', '><', $head_content);
        $head_content = trim($head_content);
        
        return $head_content;
    }
    
    /**
     * Extract and organize head content by type
     */
    public static function organize_head_content($head_content) {
        $filtered_content = self::filter_head_content($head_content);
        
        if (empty($filtered_content)) {
            return array(
                'fonts' => '',
                'styles' => '',
                'scripts' => '',
                'other' => ''
            );
        }
        
        $organized = array(
            'fonts' => '',
            'styles' => '',
            'scripts' => '',
            'other' => ''
        );
        
        // Extract Google Fonts and other font links
        preg_match_all('/<link[^>]*(?:fonts\.googleapis\.com|fonts\.gstatic\.com)[^>]*>/i', $filtered_content, $font_matches);
        if (!empty($font_matches[0])) {
            $organized['fonts'] = implode("\n", $font_matches[0]);
            $filtered_content = preg_replace('/<link[^>]*(?:fonts\.googleapis\.com|fonts\.gstatic\.com)[^>]*>/i', '', $filtered_content);
        }
        
        // Extract CSS links and style tags
        preg_match_all('/<link[^>]*rel=["\']stylesheet["\'][^>]*>/i', $filtered_content, $css_matches);
        preg_match_all('/<style[^>]*>.*?<\/style>/is', $filtered_content, $style_matches);
        
        $css_content = '';
        if (!empty($css_matches[0])) {
            $css_content .= implode("\n", $css_matches[0]) . "\n";
            $filtered_content = preg_replace('/<link[^>]*rel=["\']stylesheet["\'][^>]*>/i', '', $filtered_content);
        }
        if (!empty($style_matches[0])) {
            $css_content .= implode("\n", $style_matches[0]);
            $filtered_content = preg_replace('/<style[^>]*>.*?<\/style>/is', '', $filtered_content);
        }
        $organized['styles'] = trim($css_content);
        
        // Extract script tags
        preg_match_all('/<script[^>]*>.*?<\/script>/is', $filtered_content, $script_matches);
        if (!empty($script_matches[0])) {
            $organized['scripts'] = implode("\n", $script_matches[0]);
            $filtered_content = preg_replace('/<script[^>]*>.*?<\/script>/is', '', $filtered_content);
        }
        
        // Everything else
        $organized['other'] = trim($filtered_content);
        
        return $organized;
    }
    
    /**
     * Check if Yoast SEO is active
     */
    public static function is_yoast_seo_active() {
        return defined('WPSEO_VERSION') || class_exists('WPSEO_Options');
    }
    
    /**
     * Get optimized head content with proper priority order
     */
    public static function get_optimized_head_content($head_content) {
        $organized = self::organize_head_content($head_content);
        
        $output = '';
        
        // Add fonts first (highest priority for performance)
        if (!empty($organized['fonts'])) {
            $output .= "\n<!-- Page Generator Fonts -->\n";
            $output .= $organized['fonts'];
            $output .= "\n<!-- End Page Generator Fonts -->\n";
        }
        
        // Add CSS styles
        if (!empty($organized['styles'])) {
            $output .= "\n<!-- Page Generator Styles -->\n";
            $output .= $organized['styles'];
            $output .= "\n<!-- End Page Generator Styles -->\n";
        }
        
        // Add scripts
        if (!empty($organized['scripts'])) {
            $output .= "\n<!-- Page Generator Scripts -->\n";
            $output .= $organized['scripts'];
            $output .= "\n<!-- End Page Generator Scripts -->\n";
        }
        
        // Add other content
        if (!empty($organized['other'])) {
            $output .= "\n<!-- Page Generator Other -->\n";
            $output .= $organized['other'];
            $output .= "\n<!-- End Page Generator Other -->\n";
        }
        
        return $output;
    }
}
