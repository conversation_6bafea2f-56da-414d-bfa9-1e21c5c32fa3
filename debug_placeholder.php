<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

$post_id = 924; // Correct post ID

// Load head content filter
require_once 'C:/xampp/htdocs/WP-Theme-dev/wp-content/plugins/wordpress-plugin/includes/class-head-content-filter.php';

// Get SEO data
$seo_data = array(
    'title' => get_post_meta($post_id, '_yoast_wpseo_title', true) ?: get_the_title($post_id),
    'meta_description' => get_post_meta($post_id, '_yoast_wpseo_metadesc', true) ?: 'Professional website services with modern design and SEO optimization.',
    'focus_keyphrase' => get_post_meta($post_id, '_yoast_wpseo_focuskw', true) ?: 'website development'
);

echo "SEO Data:" . PHP_EOL;
echo "Title: " . $seo_data['title'] . PHP_EOL;
echo "Description: " . $seo_data['meta_description'] . PHP_EOL;
echo "Focus keyword: " . $seo_data['focus_keyphrase'] . PHP_EOL;
echo PHP_EOL;

// Get head content
$page_data = get_post_meta($post_id, 'page_data', true);
if (!empty($page_data) && isset($page_data['head_content'])) {
    $head_content = $page_data['head_content'];

    echo "=== STEP 1: Original content ===" . PHP_EOL;
    echo "Contains placeholder: " . (strpos($head_content, '{{Yoast_description}}') !== false ? 'YES' : 'NO') . PHP_EOL;

    // Show lines with description
    $lines = explode("\n", $head_content);
    foreach ($lines as $i => $line) {
        if (strpos($line, 'description') !== false) {
            echo "Line " . ($i + 1) . ": " . trim($line) . PHP_EOL;
        }
    }

    echo PHP_EOL . "=== STEP 2: After replacement ===" . PHP_EOL;

    // Test replacement (same as template)
    $replaced_content = str_replace('{{Yoast_title}}', $seo_data['title'], $head_content);
    $replaced_content = str_replace('{{Yoast_description}}', $seo_data['meta_description'], $replaced_content);
    $replaced_content = str_replace('{{Yoast_focus_keyword}}', $seo_data['focus_keyphrase'], $replaced_content);
    $replaced_content = str_replace('{{Yoast_keyphrase}}', $seo_data['focus_keyphrase'], $replaced_content);

    echo "Contains placeholder: " . (strpos($replaced_content, '{{Yoast_description}}') !== false ? 'YES' : 'NO') . PHP_EOL;

    // Show lines with description after replacement
    $lines = explode("\n", $replaced_content);
    foreach ($lines as $i => $line) {
        if (strpos($line, 'description') !== false) {
            echo "Line " . ($i + 1) . ": " . trim($line) . PHP_EOL;
        }
    }

    echo PHP_EOL . "=== STEP 3: After filter ===" . PHP_EOL;

    // Test filter (same as template)
    $optimized_content = PageGenerator_Head_Content_Filter::get_optimized_head_content($replaced_content);

    echo "Contains placeholder: " . (strpos($optimized_content, '{{Yoast_description}}') !== false ? 'YES' : 'NO') . PHP_EOL;
    echo "Contains meta description: " . (strpos($optimized_content, 'name="description"') !== false ? 'YES' : 'NO') . PHP_EOL;

    // Show lines with description after filter
    $lines = explode("\n", $optimized_content);
    foreach ($lines as $i => $line) {
        if (strpos($line, 'description') !== false) {
            echo "Line " . ($i + 1) . ": " . trim($line) . PHP_EOL;
        }
    }

    echo PHP_EOL . "=== FINAL OPTIMIZED CONTENT ===" . PHP_EOL;
    echo $optimized_content . PHP_EOL;
}
