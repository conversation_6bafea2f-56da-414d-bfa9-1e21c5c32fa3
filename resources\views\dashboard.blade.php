@extends('layouts.app')

@section('title', 'Dashboard')
@section('page-title', 'Dashboard')

@section('content')
<div class="space-y-6">
    <!-- Stats Grid -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        <!-- Total Campaigns -->
        <div class="bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-cyan-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-400 truncate">Total Campaigns</dt>
                            <dd class="text-lg font-medium text-white">{{ $stats['total_campaigns'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-700 px-5 py-3">
                <div class="text-sm">
                    <a href="{{ route('campaigns.index') }}" class="font-medium text-cyan-400 hover:text-cyan-300">View all campaigns</a>
                </div>
            </div>
        </div>

        <!-- Active Campaigns -->
        <div class="bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-400 truncate">Active Campaigns</dt>
                            <dd class="text-lg font-medium text-white">{{ $stats['active_campaigns'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-700 px-5 py-3">
                <div class="text-sm">
                    <a href="{{ route('campaigns.create') }}" class="font-medium text-green-400 hover:text-green-300">Create new campaign</a>
                </div>
            </div>
        </div>

        <!-- Generated Pages -->
        <div class="bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-400 truncate">Generated Pages</dt>
                            <dd class="text-lg font-medium text-white">{{ $stats['total_generated_pages'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-700 px-5 py-3">
                <div class="text-sm">
                    <a href="{{ route('content-generator.index') }}" class="font-medium text-blue-400 hover:text-blue-300">Generate content</a>
                </div>
            </div>
        </div>

        <!-- Exported Pages -->
        <div class="bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-400 truncate">Exported Pages</dt>
                            <dd class="text-lg font-medium text-white">{{ $stats['total_exported_pages'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-700 px-5 py-3">
                <div class="text-sm">
                    <a href="{{ route('export.index') }}" class="font-medium text-purple-400 hover:text-purple-300">View exports</a>
                </div>
            </div>
        </div>

        <!-- Templates -->
        <div class="bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-400 truncate">Active Templates</dt>
                            <dd class="text-lg font-medium text-white">{{ $stats['total_templates'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-700 px-5 py-3">
                <div class="text-sm">
                    <a href="{{ route('templates.index') }}" class="font-medium text-yellow-400 hover:text-yellow-300">Manage templates</a>
                </div>
            </div>
        </div>

        <!-- Recent Exports -->
        <div class="bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-400 truncate">Completed Exports</dt>
                            <dd class="text-lg font-medium text-white">{{ $stats['recent_exports'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-700 px-5 py-3">
                <div class="text-sm">
                    <a href="{{ route('export.index') }}" class="font-medium text-indigo-400 hover:text-indigo-300">View export history</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- Recent Campaigns -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">Recent Campaigns</h3>
                <div class="flow-root">
                    <ul class="-my-5 divide-y divide-gray-700">
                        @forelse($recentCampaigns as $campaign)
                            <li class="py-4">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-cyan-500 rounded-lg flex items-center justify-center">
                                            <span class="text-xs font-medium text-white">{{ substr($campaign->name, 0, 1) }}</span>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-white truncate">
                                            <a href="{{ route('campaigns.show', $campaign) }}" class="hover:text-cyan-400">{{ $campaign->name }}</a>
                                        </p>
                                        <p class="text-sm text-gray-400">
                                            {{ $campaign->generatedPages->count() }} pages • {{ $campaign->created_at->diffForHumans() }}
                                        </p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            @if($campaign->status === 'active') bg-green-800 text-green-100
                                            @elseif($campaign->status === 'completed') bg-blue-800 text-blue-100
                                            @else bg-gray-700 text-gray-300 @endif">
                                            {{ ucfirst($campaign->status) }}
                                        </span>
                                    </div>
                                </div>
                            </li>
                        @empty
                            <li class="py-4">
                                <p class="text-sm text-gray-400">No campaigns yet. <a href="{{ route('campaigns.create') }}" class="text-cyan-400 hover:text-cyan-300">Create your first campaign</a>.</p>
                            </li>
                        @endforelse
                    </ul>
                </div>
                <div class="mt-6">
                    <a href="{{ route('campaigns.index') }}" class="w-full flex justify-center items-center px-4 py-2 border border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                        View all campaigns
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Generated Pages -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">Recent Generated Pages</h3>
                <div class="flow-root">
                    <ul class="-my-5 divide-y divide-gray-700">
                        @forelse($recentPages as $page)
                            <li class="py-4">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-white truncate">{{ $page->title }}</p>
                                        <p class="text-sm text-gray-400">
                                            {{ $page->campaign->name }} • {{ $page->created_at->diffForHumans() }}
                                        </p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            @if($page->status === 'exported') bg-green-800 text-green-100
                                            @else bg-gray-700 text-gray-300 @endif">
                                            {{ ucfirst($page->status) }}
                                        </span>
                                    </div>
                                </div>
                            </li>
                        @empty
                            <li class="py-4">
                                <p class="text-sm text-gray-400">No pages generated yet. <a href="{{ route('content-generator.index') }}" class="text-blue-400 hover:text-blue-300">Start generating content</a>.</p>
                            </li>
                        @endforelse
                    </ul>
                </div>
                <div class="mt-6">
                    <a href="{{ route('content-generator.index') }}" class="w-full flex justify-center items-center px-4 py-2 border border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                        Generate more content
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
