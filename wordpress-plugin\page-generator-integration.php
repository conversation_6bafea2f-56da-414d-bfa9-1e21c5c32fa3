<?php
/**
 * Plugin Name: Page Generator Integration
 * Plugin URI: https://yourwebsite.com/page-generator-integration
 * Description: WordPress integration plugin for Laravel Page Generator. Handles static section shortcodes and content management.
 * Version: 1.1.0
 * Author: Your Name
 * License: GPL v2 or later
 * Text Domain: page-generator-integration
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Activation hook for flushing rewrite rules.
 * This is crucial for the custom post type's flat URLs to work immediately.
 */
register_activation_hook(__FILE__, 'page_generator_activate');
function page_generator_activate() {
    // Ensure the CPT is registered before flushing
    PageGeneratorIntegration::register_landing_page_cpt();
    // Flush the rewrite rules to apply the new CPT slug settings
    flush_rewrite_rules();
}

class PageGeneratorIntegration {

    private $static_sections = [];
    private $shortcode_data = array();
    private $custom_sections;
    private $image_generator;

    public function __construct() {
        // Load required classes
        $this->load_dependencies();

        add_action('init', array($this, 'init'));
        add_action('init', array($this, 'register_landing_page_cpt')); // Register CPT
        add_action('init', array($this, 'add_custom_rewrite_rules')); // Custom URL rules

        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('wp_ajax_save_static_section', array($this, 'save_static_section'));
        add_action('wp_ajax_delete_static_section', array($this, 'delete_static_section'));
        add_action('wp_ajax_get_static_section', array($this, 'get_static_section'));

        add_action('init', array($this, 'register_shortcodes'), 5);
        add_action('wp_loaded', array($this, 'register_shortcodes'), 5);
        add_action('wp', array($this, 'register_shortcodes'), 5);

        // Handle Laravel API integration - NEW CLEAN APPROACH
        add_action('wp_ajax_nopriv_receive_laravel_clean_data', array($this, 'receive_laravel_clean_data'));
        add_action('wp_ajax_receive_laravel_clean_data', array($this, 'receive_laravel_clean_data'));

        // Legacy endpoints (backward compatibility)
        add_action('wp_ajax_nopriv_receive_laravel_data', array($this, 'receive_laravel_data'));
        add_action('wp_ajax_receive_laravel_data', array($this, 'receive_laravel_data'));
        add_action('wp_ajax_nopriv_receive_laravel_head_main_data', array($this, 'receive_laravel_head_main_data'));
        add_action('wp_ajax_receive_laravel_head_main_data', array($this, 'receive_laravel_head_main_data'));
        add_action('wp_ajax_nopriv_save_static_section_from_laravel', array($this, 'handle_static_section_ajax'));
        add_action('wp_ajax_save_static_section_from_laravel', array($this, 'handle_static_section_ajax'));

        // Register REST API endpoints
        add_action('rest_api_init', array($this, 'register_rest_endpoints'));

        // Template handling for CPT
        add_filter('template_include', array($this, 'use_landing_page_template'));

        // Content filters
        add_filter('the_content', array($this, 'process_shortcodes_in_content'), 99);
        add_filter('widget_text', 'do_shortcode');
        add_filter('the_excerpt', 'do_shortcode');

        // Inject assets for landing pages - NEW CLEAN APPROACH
        add_action('wp_head', array($this, 'inject_page_assets'), 15);

        // Custom permalink for flat URLs
        add_filter('post_type_link', array($this, 'custom_landing_page_permalink'), 10, 2);
    }

    /**
     * Load required dependencies
     */
    private function load_dependencies() {
        require_once plugin_dir_path(__FILE__) . 'includes/class-custom-sections.php';
        require_once plugin_dir_path(__FILE__) . 'includes/class-image-generator.php';
        require_once plugin_dir_path(__FILE__) . 'includes/class-seo-optimizer.php';
        require_once plugin_dir_path(__FILE__) . 'includes/class-template-handler.php';
        require_once plugin_dir_path(__FILE__) . 'includes/class-head-content-filter.php';
        require_once plugin_dir_path(__FILE__) . 'admin/admin-page.php';

        $this->custom_sections = new PageGenerator_Custom_Sections();
        $this->image_generator = new PageGenerator_Image_Generator();
    }

    /**
     * Register the 'landing_page' Custom Post Type.
     */
    public static function register_landing_page_cpt() {
        $labels = array(
            'name'               => _x('Landing Pages', 'post type general name', 'page-generator-integration'),
            'singular_name'      => _x('Landing Page', 'post type singular name', 'page-generator-integration'),
            'menu_name'          => _x('Landing Pages', 'admin menu', 'page-generator-integration'),
            'name_admin_bar'     => _x('Landing Page', 'add new on admin bar', 'page-generator-integration'),
            'add_new'            => _x('Add New', 'landing page', 'page-generator-integration'),
            'add_new_item'       => __('Add New Landing Page', 'page-generator-integration'),
            'new_item'           => __('New Landing Page', 'page-generator-integration'),
            'edit_item'          => __('Edit Landing Page', 'page-generator-integration'),
            'view_item'          => __('View Landing Page', 'page-generator-integration'),
            'all_items'          => __('All Landing Pages', 'page-generator-integration'),
            'search_items'       => __('Search Landing Pages', 'page-generator-integration'),
            'not_found'          => __('No landing pages found.', 'page-generator-integration'),
            'not_found_in_trash' => __('No landing pages found in Trash.', 'page-generator-integration')
        );

        $args = array(
            'labels'             => $labels,
            'public'             => true,
            'has_archive'        => false,
            'rewrite'            => false, // Disable default rewrite for custom flat URLs
            'hierarchical'       => false,
            'supports'           => array('title', 'editor', 'thumbnail'),
            'show_in_rest'       => true,
            'menu_icon'          => 'dashicons-welcome-widgets-menus',
        );

        register_post_type('landing_page', $args);
    }

    /**
     * Add custom rewrite rules for flat URLs without /landing_page/
     */
    public function add_custom_rewrite_rules() {
        // Add rewrite rule for landing pages to be accessible directly from root
        // This will match any URL that doesn't match existing WordPress rules
        add_rewrite_rule(
            '^([^/]+)/?$',
            'index.php?post_type=landing_page&name=$matches[1]',
            'top'
        );

        // Flush rewrite rules if needed
        if (get_option('page_generator_flush_rules') !== 'done') {
            flush_rewrite_rules();
            update_option('page_generator_flush_rules', 'done');
        }
    }

    /**
     * Custom permalink for landing pages to create flat URLs
     */
    public function custom_landing_page_permalink($post_link, $post) {
        if ($post->post_type === 'landing_page' && $post->post_status === 'publish') {
            $post_link = home_url('/' . $post->post_name . '/');
        }
        return $post_link;
    }

    /**
     * Generate SEO-friendly short slug from title
     */
    public function generate_short_slug($title, $max_length = 35) {
        // Remove common words for SEO (Indonesian and English)
        $stop_words = array(
            'dan', 'atau', 'untuk', 'dengan', 'yang', 'di', 'ke', 'dari', 'pada', 'dalam', 'adalah', 'akan', 'telah', 'sudah', 'juga', 'dapat', 'bisa', 'harus', 'tidak', 'belum', 'masih', 'sangat', 'lebih', 'paling', 'saja', 'hanya', 'sekali', 'kali', 'banyak', 'sedikit', 'jasa', 'layanan', 'terbaik', 'terpercaya', 'profesional',
            'the', 'and', 'or', 'for', 'with', 'that', 'in', 'to', 'from', 'on', 'at', 'is', 'are', 'was', 'were', 'will', 'have', 'has', 'had', 'can', 'could', 'should', 'would', 'may', 'might', 'must', 'shall', 'do', 'does', 'did', 'a', 'an', 'this', 'that', 'these', 'those', 'best', 'professional', 'service', 'services'
        );

        // Convert to lowercase and remove special characters
        $slug = strtolower($title);
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);

        // Split into words
        $words = explode(' ', $slug);
        $filtered_words = array();

        // Remove stop words but keep important keywords
        foreach ($words as $word) {
            if (strlen($word) > 2 && !in_array($word, $stop_words)) {
                $filtered_words[] = $word;
            }
        }

        // If too few words remain, use original words
        if (count($filtered_words) < 2) {
            $filtered_words = array_slice($words, 0, 4);
        }

        // Join words and limit length
        $slug = implode('-', $filtered_words);
        if (strlen($slug) > $max_length) {
            $slug = substr($slug, 0, $max_length);
            // Cut at last dash to avoid broken words
            $last_dash = strrpos($slug, '-');
            if ($last_dash !== false) {
                $slug = substr($slug, 0, $last_dash);
            }
        }

        return $slug;
    }

    /**
     * Generate and set featured image for landing page
     */
    private function generate_and_set_featured_image($post_id, $page_data) {
        if (!$this->image_generator) {
            return false;
        }

        $title = $page_data['title'] ?? 'Landing Page';
        $seo_keyword = $page_data['seo_keyword'] ?? '';

        // Generate image with title and keyword
        $image_data = $this->image_generator->generate_image($title, $seo_keyword);

        if (!$image_data) {
            return false;
        }

        // Create filename based on slug
        $slug = get_post_field('post_name', $post_id);
        $filename = sanitize_file_name($slug . '-featured.jpg');

        // Upload image to WordPress media library
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['path'] . '/' . $filename;

        // Save image data to file
        if (file_put_contents($file_path, $image_data) === false) {
            return false;
        }

        // Create attachment
        $attachment = array(
            'guid' => $upload_dir['url'] . '/' . $filename,
            'post_mime_type' => 'image/jpeg',
            'post_title' => $title,
            'post_content' => '',
            'post_status' => 'inherit'
        );

        $attachment_id = wp_insert_attachment($attachment, $file_path, $post_id);

        if (is_wp_error($attachment_id)) {
            return false;
        }

        // Generate attachment metadata
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attachment_data = wp_generate_attachment_metadata($attachment_id, $file_path);
        wp_update_attachment_metadata($attachment_id, $attachment_data);

        // Set as featured image
        set_post_thumbnail($post_id, $attachment_id);

        // Update alt text for SEO
        $alt_text = $title;
        if (!empty($seo_keyword)) {
            $alt_text .= ' - ' . $seo_keyword;
        }
        update_post_meta($attachment_id, '_wp_attachment_image_alt', $alt_text);

        return $attachment_id;
    }

    /**
     * Load a custom template for the 'landing_page' CPT.
     */
    public function use_landing_page_template($template) {
        if (is_singular('landing_page')) {
            $plugin_template = plugin_dir_path(__FILE__) . 'templates/single-landing_page.php';
            if (file_exists($plugin_template)) {
                return $plugin_template;
            }
        }
        return $template;
    }
    
    /**
     * Create WordPress page with head/main separation as a 'landing_page' CPT.
     */
    private function create_head_main_wordpress_page($page_data) {
        // Find an administrator to be the author
        $admin_user = get_users(array('role' => 'administrator', 'number' => 1, 'orderby' => 'ID'));
        $author_id = !empty($admin_user) ? $admin_user[0]->ID : 1;

        // Generate SEO-friendly short slug using the new SEO optimizer
        $title = $page_data['title'] ?? 'Landing Page';
        if (class_exists('PageGenerator_SEO_Optimizer')) {
            $short_slug = PageGenerator_SEO_Optimizer::generate_seo_slug($title);
        } else {
            $short_slug = $this->generate_short_slug($title);
        }

        // Slug conflict guard: ensure the slug is unique across all post types
        $slug = $short_slug;
        $original_slug = $slug;
        $counter = 2;
        while (get_page_by_path($slug, OBJECT, 'any')) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }

        // Prepare SEO data structure
        $seo_data = array(
            'title' => $page_data['seo_title'] ?? $page_data['title'] ?? '',
            'meta_description' => $page_data['seo_description'] ?? '',
            'focus_keyphrase' => $page_data['seo_keyword'] ?? $page_data['focus_keyphrase'] ?? ''
        );

        // Prepare the page arguments for the CPT
        $page_args = array(
            'post_title'   => $page_data['title'],
            'post_content' => $page_data['main_content'] ?? '',
            'post_status'  => $page_data['status'] ?? 'publish',
            'post_type'    => 'landing_page', // Use the new CPT
            'post_author'  => $author_id,
            'post_name'    => $slug, // Use the unique slug
            'meta_input'   => array(
                // New structured data format
                'page_data' => $page_data,
                'seo_data' => $seo_data,

                // Backward compatibility with old format
                'page_generator_source'       => 'laravel_head_main',
                'page_generator_head_content' => $page_data['head_content'] ?? '',
                'page_generator_main_content' => $page_data['main_content'] ?? '',
                'page_generator_template_id'  => $page_data['template_id'] ?? '',
                'page_generator_page_id'      => $page_data['page_id'] ?? '',
            )
        );

        // Temporarily disable kses filtering to allow raw HTML in meta
        remove_filter('content_save_pre', 'wp_filter_post_kses');
        remove_filter('content_filtered_save_pre', 'wp_filter_post_kses');

        $post_id = wp_insert_post($page_args, true);

        // Re-enable kses filtering for normal operations
        add_filter('content_save_pre', 'wp_filter_post_kses');
        add_filter('content_filtered_save_pre', 'wp_filter_post_kses');

        if (is_wp_error($post_id)) {
            error_log('Page Generator Plugin Error: ' . $post_id->get_error_message());
            return 0;
        }

        // Update Yoast SEO meta fields using the new SEO optimizer
        if (class_exists('PageGenerator_SEO_Optimizer')) {
            PageGenerator_SEO_Optimizer::update_yoast_meta($post_id, $seo_data);
        } else {
            // Fallback to direct meta updates
            update_post_meta($post_id, '_yoast_wpseo_title', $seo_data['title']);
            update_post_meta($post_id, '_yoast_wpseo_metadesc', $seo_data['meta_description']);
            update_post_meta($post_id, '_yoast_wpseo_focuskw', $seo_data['focus_keyphrase']);
        }

        // Generate and set featured image
        $this->generate_and_set_featured_image($post_id, $page_data);

        return $post_id;
    }

    /**
     * NEW CLEAN APPROACH: Create WordPress page with separated content and assets
     */
    public function create_clean_wordpress_page($page_data) {
        // Find an administrator to be the author
        $admin_user = get_users(array('role' => 'administrator', 'number' => 1, 'orderby' => 'ID'));
        $author_id = !empty($admin_user) ? $admin_user[0]->ID : 1;

        // Generate SEO-friendly slug
        $title = $page_data['title'] ?? 'Landing Page';
        if (class_exists('PageGenerator_SEO_Optimizer')) {
            $short_slug = PageGenerator_SEO_Optimizer::generate_seo_slug($title);
        } else {
            $short_slug = $this->generate_short_slug($title);
        }

        // Ensure unique slug
        $slug = $short_slug;
        $original_slug = $slug;
        $counter = 2;
        while (get_page_by_path($slug, OBJECT, 'any')) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }

        // Prepare SEO data
        $seo_data = array(
            'title' => $page_data['seo_title'] ?? $page_data['title'] ?? '',
            'meta_description' => $page_data['seo_description'] ?? '',
            'focus_keyphrase' => $page_data['seo_keyword'] ?? ''
        );

        // Create the page with CLEAN SEPARATION
        $page_args = array(
            'post_title'   => $page_data['title'],
            'post_content' => $page_data['html_content'], // HTML content goes to post_content (user can edit)
            'post_status'  => 'publish',
            'post_type'    => 'landing_page',
            'post_author'  => $author_id,
            'post_name'    => $slug,
            'meta_input'   => array(
                // Assets go to post meta (safe from editor)
                '_page_generator_head_content' => $page_data['assets_content'],
                '_page_generator_source' => 'laravel_clean',
                '_page_generator_template_id' => $page_data['template_id'] ?? '',
                '_page_generator_page_id' => $page_data['page_id'] ?? '',

                // SEO data
                'seo_data' => $seo_data
            )
        );

        // Insert the post
        $post_id = wp_insert_post($page_args);

        if (is_wp_error($post_id)) {
            error_log('WordPress Plugin - Error creating clean page: ' . $post_id->get_error_message());
            return 0;
        }

        // Update Yoast SEO meta fields
        if (class_exists('PageGenerator_SEO_Optimizer')) {
            PageGenerator_SEO_Optimizer::update_yoast_meta($post_id, $seo_data);
        } else {
            // Fallback to direct meta updates
            update_post_meta($post_id, '_yoast_wpseo_title', $seo_data['title']);
            update_post_meta($post_id, '_yoast_wpseo_metadesc', $seo_data['meta_description']);
            update_post_meta($post_id, '_yoast_wpseo_focuskw', $seo_data['focus_keyphrase']);
        }

        // Generate and set featured image
        $this->generate_and_set_featured_image($post_id, $page_data);

        return $post_id;
    }

    /**
     * Inject assets for landing pages - NEW CLEAN APPROACH
     */
    public function inject_page_assets() {
        // Only inject on single 'landing_page' CPTs
        if (!is_singular('landing_page')) {
            return;
        }

        global $post;

        // Get assets from post meta
        $assets_content = get_post_meta($post->ID, '_page_generator_head_content', true);

        if (!empty($assets_content)) {
            // Use the existing filter to clean up and optimize assets
            if (class_exists('PageGenerator_Head_Content_Filter')) {
                $optimized_assets = PageGenerator_Head_Content_Filter::get_optimized_head_content($assets_content);
            } else {
                $optimized_assets = $assets_content;
            }

            if (!empty($optimized_assets)) {
                echo "\n<!-- Page Generator Assets (Clean Approach) -->\n";
                echo $optimized_assets;
                echo "\n<!-- End Page Generator Assets -->\n";
            }
        }
    }

    /**
     * LEGACY: Inject head elements for 'landing_page' CPT (backward compatibility)
     */
    public function inject_page_head_elements() {
        // Only inject on single 'landing_page' CPTs
        if (!is_singular('landing_page')) {
            return;
        }

        global $post;

        // Check if we're using new data structure first
        $page_data = get_post_meta($post->ID, 'page_data', true);
        $head_content = '';

        if (!empty($page_data) && isset($page_data['head_content'])) {
            // Skip injection here - let the template handle it with proper filtering
            return;
        } else {
            // Fallback to old structure with filtering
            $head_content = get_post_meta($post->ID, 'page_generator_head_content', true);
        }

        if (!empty($head_content)) {
            // Use the new filter to clean up head content
            $optimized_head_content = PageGenerator_Head_Content_Filter::get_optimized_head_content($head_content);

            if (!empty($optimized_head_content)) {
                echo $optimized_head_content;
            }
        }
    }

    // --- ALL OTHER METHODS FROM THE ORIGINAL FILE GO HERE ---
    // To keep this concise, I'm omitting the unchanged methods like
    // init, add_admin_menu, sections_page, receive_laravel_head_main_data, etc.
    // They are assumed to be present and correct in the final file.
    // The 'create_head_main_wordpress_page' and 'inject_page_head_elements'
    // have been fully replaced above.
    
    public function init() {
        $this->load_static_sections();
    }
    
    public function add_admin_menu() {
        add_menu_page('Page Generator', 'Page Generator', 'manage_options', 'page-generator', array($this, 'admin_page'), 'dashicons-layout', 30);
        add_submenu_page('page-generator', 'Static Sections', 'Static Sections', 'manage_options', 'page-generator-sections', array($this, 'sections_page'));
    }
    
    public function admin_page() { /* ... original content ... */ }
    public function sections_page() { /* ... original content ... */ }
    private function handle_section_save() { /* ... original content ... */ }
    private function delete_section($shortcode_name) { /* ... original content ... */ }
    private function display_sections_table() { /* ... original content ... */ }
    private function display_recent_activity() { /* ... original content ... */ }
    public function register_test_shortcode() { /* ... original content ... */ }
    public function register_shortcodes() { /* ... original content ... */ }
    public function render_static_shortcode($atts, $content, $tag) { /* ... original content ... */ }
    public function process_shortcodes_in_content($content) { /* ... original content ... */ }
    public function register_rest_endpoints() { /* ... original content ... */ }
    public function handle_test_api($request) { /* ... original content ... */ }
    public function check_api_permissions() { /* ... original content ... */ }
    private function verify_api_key() { /* ... original content ... */ }
    public function handle_static_section_api($request) { /* ... original content ... */ }
    public function receive_laravel_data() { /* ... original content ... */ }
    /**
     * NEW CLEAN APPROACH: Receive separated HTML content and assets from Laravel
     */
    public function receive_laravel_clean_data() {
        if (!isset($_POST['action']) || $_POST['action'] !== 'receive_laravel_clean_data') {
            wp_die('Invalid request');
        }

        error_log('WordPress Plugin - Received clean data: ' . print_r($_POST, true));

        $page_data = json_decode(stripslashes($_POST['page_data']), true);
        if (!$page_data) {
            error_log('WordPress Plugin - Failed to decode page_data: ' . $_POST['page_data']);
            wp_send_json_error('Invalid page data');
        }

        error_log('WordPress Plugin - Decoded clean page_data: ' . print_r($page_data, true));

        $page_id = $this->create_clean_wordpress_page($page_data);
        if ($page_id > 0) {
            $this->log_activity('Landing Page created from Laravel (Clean): ' . $page_data['title']);
            wp_send_json_success(array(
                'page_id' => $page_id,
                'page_url' => get_permalink($page_id),
                'message' => 'Landing Page created successfully with clean separation'
            ));
        } else {
            error_log('WordPress Plugin - Failed to create clean page, page_id: ' . $page_id);
            wp_send_json_error('Failed to create Landing Page.');
        }
    }

    /**
     * LEGACY: Receive head/main data from Laravel (backward compatibility)
     */
    public function receive_laravel_head_main_data() {
        if (!isset($_POST['action']) || $_POST['action'] !== 'receive_laravel_head_main_data') {
            wp_die('Invalid request');
        }

        // Log received data for debugging
        error_log('WordPress Plugin - Received legacy data: ' . print_r($_POST, true));

        $page_data = json_decode(stripslashes($_POST['page_data']), true);
        if (!$page_data) {
            error_log('WordPress Plugin - Failed to decode page_data: ' . $_POST['page_data']);
            wp_send_json_error('Invalid page data');
        }

        error_log('WordPress Plugin - Decoded legacy page_data: ' . print_r($page_data, true));

        $page_id = $this->create_head_main_wordpress_page($page_data);
        if ($page_id > 0) {
            $this->log_activity('Landing Page created from Laravel (Legacy): ' . $page_data['title']);
            wp_send_json_success(array('page_id' => $page_id, 'page_url' => get_permalink($page_id), 'message' => 'Landing Page created successfully'));
        } else {
            error_log('WordPress Plugin - Failed to create legacy page, page_id: ' . $page_id);
            wp_send_json_error('Failed to create Landing Page.');
        }
    }
    public function handle_static_section_ajax() { /* ... original content ... */ }
    private function create_wordpress_page($page_data) { /* ... original content ... */ }
    private function extract_html_components($complete_html) { /* ... original content ... */ }
    public function enqueue_page_generator_assets() { /* ... original content ... */ }
    private function log_activity($message) { /* ... original content ... */ }
    private function load_static_sections() { /* ... original content ... */ }
    public function get_static_section() { /* ... original content ... */ }
}

// Initialize the plugin
new PageGeneratorIntegration();

// Enqueue admin assets
add_action('admin_enqueue_scripts', function($hook) {
    if (strpos($hook, 'page-generator') === false) return;
    $plugin_url = plugin_dir_url(__FILE__);
    wp_enqueue_style('page-generator-admin', $plugin_url . 'assets/css/admin-style.css', array(), '1.1.0');
    wp_enqueue_script('page-generator-admin', $plugin_url . 'assets/js/admin-script.js', array('jquery'), '1.1.0', true);
    wp_localize_script('page-generator-admin', 'pageGeneratorAdmin', array('nonce' => wp_create_nonce('page_generator_nonce'), 'ajaxurl' => admin_url('admin-ajax.php')));
});
?>