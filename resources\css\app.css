@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

/* Dark Theme Global Styles */
:root {
    --bg-primary: #1E1E1E;
    --bg-secondary: #2A2A2A;
    --bg-tertiary: #2E2E2E;
    --accent-primary: #00ADB5;
    --accent-secondary: #393E46;
    --text-primary: #EEEEEE;
    --text-secondary: #B8B8B8;
    --border-color: #393E46;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-secondary);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-primary);
}

/* Custom button styles */
.btn-primary {
    @apply bg-cyan-600 hover:bg-cyan-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.btn-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-gray-300 font-medium py-2 px-4 rounded-md border border-gray-600 transition-colors duration-200;
}

/* Custom form styles */
.form-input {
    @apply bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500;
}

.form-textarea {
    @apply bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-cyan-500 focus:border-cyan-500;
}

/* Custom card styles */
.card {
    @apply bg-gray-800 shadow rounded-lg;
}

.card-header {
    @apply px-4 py-5 sm:p-6 border-b border-gray-700;
}

.card-body {
    @apply px-4 py-5 sm:p-6;
}

/* Status badges */
.badge-success {
    @apply bg-green-800 text-green-100 px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-warning {
    @apply bg-yellow-800 text-yellow-100 px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-error {
    @apply bg-red-800 text-red-100 px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-info {
    @apply bg-blue-800 text-blue-100 px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-default {
    @apply bg-gray-700 text-gray-300 px-2.5 py-0.5 rounded-full text-xs font-medium;
}
