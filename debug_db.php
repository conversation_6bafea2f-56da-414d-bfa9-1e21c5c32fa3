<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

global $wpdb;
$table_name = $wpdb->prefix . 'page_generator_sections';

echo "=== CHECKING DATABASE TABLE ===" . PHP_EOL;

$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
echo 'Table exists: ' . ($table_exists ? 'YES' : 'NO') . PHP_EOL;

if ($table_exists) {
    $count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    echo 'Records count: ' . $count . PHP_EOL;
    
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    echo 'Table structure:' . PHP_EOL;
    foreach ($columns as $column) {
        echo '  - ' . $column->Field . ' (' . $column->Type . ')' . PHP_EOL;
    }
    
    if ($count > 0) {
        echo PHP_EOL . 'Sample records:' . PHP_EOL;
        $records = $wpdb->get_results("SELECT * FROM $table_name LIMIT 3");
        foreach ($records as $record) {
            echo "  ID: {$record->id} | Name: {$record->name} | Shortcode: {$record->shortcode}" . PHP_EOL;
        }
    }
} else {
    echo "Table does not exist. Need to create it." . PHP_EOL;
}
