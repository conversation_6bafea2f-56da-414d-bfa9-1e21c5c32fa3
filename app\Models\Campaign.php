<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Campaign extends Model
{
    protected $fillable = [
        'name',
        'description',
        'main_keyword',
        'business_name',
        'contact_phone',
        'contact_email',
        'address',
        'business_description',
        'people_also_ask',
        'keywords_data',
        'master_prompt',
        'campaign_data',
        'status',
        'wordpress_connection_id',
        'template_id',
    ];

    protected $casts = [
        'people_also_ask' => 'array',
        'keywords_data' => 'array',
        'campaign_data' => 'array',
    ];

    /**
     * Get the generated pages for the campaign.
     */
    public function generatedPages(): Has<PERSON>any
    {
        return $this->hasMany(GeneratedPage::class);
    }

    /**
     * Get the export logs for the campaign.
     */
    public function exportLogs(): HasMany
    {
        return $this->hasMany(ExportLog::class);
    }

    /**
     * Get the WordPress connection for the campaign.
     */
    public function wordpressConnection(): BelongsTo
    {
        return $this->belongsTo(WordPressConnection::class);
    }

    /**
     * Get the template for the campaign.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class);
    }

    /**
     * Generate master prompt based on campaign data.
     */
    public function generateMasterPrompt(): string
    {
        $prompt = "Generate landing page content for the following business:\n\n";
        $prompt .= "Business Name: {$this->business_name}\n";
        $prompt .= "Main Keyword: {$this->main_keyword}\n";
        $prompt .= "Business Description: {$this->business_description}\n";

        if ($this->contact_phone) {
            $prompt .= "Contact Phone: {$this->contact_phone}\n";
        }

        if ($this->contact_email) {
            $prompt .= "Contact Email: {$this->contact_email}\n";
        }

        if ($this->address) {
            $prompt .= "Address: {$this->address}\n";
        }

        if ($this->people_also_ask && count($this->people_also_ask) > 0) {
            $prompt .= "\nFAQ Questions (People Also Ask):\n";
            foreach ($this->people_also_ask as $index => $question) {
                $prompt .= ($index + 1) . ". {$question}\n";
            }
        }

        $prompt .= "\nPlease generate a JSON array with multiple variations of landing page content. Each object should include:\n";
        $prompt .= "- Hero_headline: Main headline for the hero section\n";
        $prompt .= "- Sub_headline: Supporting headline\n";
        $prompt .= "- FAQ: Array of FAQ objects with 'q' (question) and 'a' (answer)\n";
        $prompt .= "- Yoast_title: SEO title for Yoast\n";
        $prompt .= "- Yoast_description: Meta description for Yoast\n";
        $prompt .= "- Yoast_focus_keyword: Focus keyword for Yoast SEO\n";
        $prompt .= "- Contact: Contact information\n";
        $prompt .= "- Address: Business address\n";

        return $prompt;
    }

    /**
     * Generate combined master prompt with template context.
     */
    public function generateCombinedMasterPrompt(Template $template): string
    {
        // Get template JSON structure
        $templateJson = $template->generateMasterPrompt();
        $templateData = json_decode($templateJson, true);

        if (!$templateData) {
            return "Error: Invalid template structure";
        }

        // Build business context
        $businessContext = [
            'business_name' => $this->business_name,
            'main_keyword' => $this->main_keyword,
            'business_description' => $this->business_description,
            'contact_phone' => $this->contact_phone,
            'contact_email' => $this->contact_email,
            'address' => $this->address
        ];

        // Add FAQ information
        $faqs = [];
        if ($this->people_also_ask && is_array($this->people_also_ask)) {
            $faqs = $this->people_also_ask;
        }

        // Create comprehensive, AI-friendly prompt
        $prompt = "# LANDING PAGE CONTENT GENERATOR\n\n";

        $prompt .= "## BUSINESS INFORMATION\n";
        $prompt .= "**Business Name:** {$this->business_name}\n";
        $prompt .= "**Main Keyword:** {$this->main_keyword}\n";
        if ($this->business_description) {
            $prompt .= "**Description:** {$this->business_description}\n";
        }
        if ($this->contact_phone) {
            $prompt .= "**Phone:** {$this->contact_phone}\n";
        }
        if ($this->contact_email) {
            $prompt .= "**Email:** {$this->contact_email}\n";
        }
        if ($this->address) {
            $prompt .= "**Address:** {$this->address}\n";
        }

        if (!empty($faqs)) {
            $prompt .= "**FAQ Topics:** " . implode(', ', $faqs) . "\n";
        }

        $prompt .= "\n## TASK INSTRUCTIONS\n";
        $prompt .= "Generate **multiple variations** of landing page content based on the keyword '{$this->main_keyword}'. ";
        $prompt .= "Create content that is:\n";
        $prompt .= "- **Persuasive** and conversion-focused\n";
        $prompt .= "- **SEO-optimized** for the main keyword\n";
        $prompt .= "- **Localized** for Indonesian market\n";
        $prompt .= "- **Professional** yet engaging tone\n";
        $prompt .= "- **Unique** for each variation\n\n";

        $prompt .= "## OUTPUT FORMAT\n";
        $prompt .= "Return your response as a **JSON array** containing multiple objects. Each object represents one complete landing page variation.\n\n";

        $prompt .= "## EXAMPLE FORMAT\n";
        $prompt .= "```json\n";
        $prompt .= "[\n";
        $prompt .= "  {\n";
        $prompt .= "    \"Yoast_title\": \"SEO title variation 1\",\n";
        $prompt .= "    \"Hero_headline\": \"Main headline variation 1\",\n";
        $prompt .= "    \"FAQ\": [\n";
        $prompt .= "      {\"q\": \"Question 1?\", \"a\": \"Answer 1\"},\n";
        $prompt .= "      {\"q\": \"Question 2?\", \"a\": \"Answer 2\"}\n";
        $prompt .= "    ],\n";
        $prompt .= "    \"Contact\": \"{$this->contact_phone}\",\n";
        $prompt .= "    \"Address\": \"{$this->address}\",\n";
        $prompt .= "    \"Email\": \"{$this->contact_email}\"\n";
        $prompt .= "  },\n";
        $prompt .= "  {\n";
        $prompt .= "    \"Yoast_title\": \"SEO title variation 2\",\n";
        $prompt .= "    \"Hero_headline\": \"Main headline variation 2\",\n";
        $prompt .= "    // ... all other fields with different content\n";
        $prompt .= "  }\n";
        $prompt .= "]\n";
        $prompt .= "```\n\n";

        $prompt .= "## REQUIRED FIELDS TO FILL\n";
        $prompt .= "Fill ALL the following fields for each variation:\n\n";

        // Create context-aware placeholder descriptions
        $contextualPlaceholders = $this->createContextualPlaceholders($templateData, $businessContext);

        $prompt .= json_encode($contextualPlaceholders, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        $prompt .= "\n\n## IMPORTANT NOTES\n";
        $prompt .= "- Generate **2-3 complete variations** with different content approaches\n";
        $prompt .= "- Each variation should target the same keyword but with different angles\n";
        $prompt .= "- Ensure all contact information is consistent across variations\n";
        $prompt .= "- FAQ should contain relevant questions for '{$this->main_keyword}' business\n";
        $prompt .= "- All content should be in **Indonesian language**\n";
        $prompt .= "- Return **ONLY** the JSON array, no additional text";

        return $prompt;
    }

    /**
     * Create contextual placeholder descriptions based on business context.
     */
    private function createContextualPlaceholders(array $templateData, array $businessContext): array
    {
        $contextualData = $templateData;

        // Add business context to SEO fields
        if (isset($contextualData['seo_fields'])) {
            $contextualData['seo_fields']['yoast_title'] = "SEO title for {$businessContext['business_name']} - {$businessContext['main_keyword']}";
            $contextualData['seo_fields']['yoast_description'] = "Meta description for {$businessContext['business_name']} services";
            $contextualData['seo_fields']['yoast_focus_keyword'] = $businessContext['main_keyword'];
            $contextualData['seo_fields']['schema_business_name'] = $businessContext['business_name'];
            $contextualData['seo_fields']['schema_business_type'] = "Service business";
            $contextualData['seo_fields']['schema_description'] = $businessContext['business_description'] ?? "Professional services";
            $contextualData['seo_fields']['schema_telephone'] = $businessContext['contact_phone'] ?? "";
            $contextualData['seo_fields']['schema_address'] = $businessContext['address'] ?? "";
            $contextualData['seo_fields']['schema_email'] = $businessContext['contact_email'] ?? "";
        }

        // Add business context to contact fields
        if (isset($contextualData['content_fields'])) {
            $contextualData['content_fields']['Contact'] = $businessContext['contact_phone'] ?? "";
            $contextualData['content_fields']['Address'] = $businessContext['address'] ?? "";
            $contextualData['content_fields']['Email'] = $businessContext['contact_email'] ?? "";
        }

        return $contextualData;
    }
}
