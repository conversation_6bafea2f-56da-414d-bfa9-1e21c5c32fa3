<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

// Test database connection and table
global $wpdb;
$table_name = $wpdb->prefix . 'custom_sections';

// Check if table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
echo 'Table exists: ' . ($table_exists ? 'YES' : 'NO') . PHP_EOL;

if ($table_exists) {
    // Test insert
    $result = $wpdb->insert(
        $table_name,
        array(
            'name' => 'Test Section',
            'shortcode' => 'test_section',
            'description' => 'Test description',
            'html_content' => '<div>Test HTML</div>',
            'css_content' => '',
            'js_content' => '',
            'created_at' => current_time('mysql')
        ),
        array('%s', '%s', '%s', '%s', '%s', '%s', '%s')
    );
    
    if ($result === false) {
        echo 'Insert failed: ' . $wpdb->last_error . PHP_EOL;
    } else {
        echo 'Insert successful. ID: ' . $wpdb->insert_id . PHP_EOL;
        
        // Test select
        $sections = $wpdb->get_results("SELECT * FROM $table_name");
        echo 'Total sections: ' . count($sections) . PHP_EOL;
        
        foreach ($sections as $section) {
            echo 'Section: ' . $section->name . ' | Shortcode: ' . $section->shortcode . PHP_EOL;
        }
    }
}
