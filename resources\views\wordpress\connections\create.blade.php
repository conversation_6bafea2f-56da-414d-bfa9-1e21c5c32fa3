@extends('layouts.app')

@section('title', 'Add WordPress Connection')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center mb-4">
            <a href="{{ route('wordpress.connections.index') }}" 
               class="text-gray-400 hover:text-white transition-colors mr-4">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-3xl font-bold text-white">Add WordPress Connection</h1>
        </div>
        <p class="text-gray-400">Connect your WordPress site to enable content export</p>
    </div>

    <!-- Form -->
    <div class="bg-[#2A2A2A] rounded-lg p-8">
        <form action="{{ route('wordpress.connections.store') }}" method="POST" id="connectionForm">
            @csrf
            
            <!-- Connection Name -->
            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-gray-300 mb-2">
                    Connection Name <span class="text-red-400">*</span>
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       value="{{ old('name') }}"
                       class="w-full px-4 py-3 bg-[#1E1E1E] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-[#00ADB5] focus:ring-1 focus:ring-[#00ADB5] transition-colors"
                       placeholder="e.g., My WordPress Site"
                       required>
                @error('name')
                    <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- WordPress URL -->
            <div class="mb-6">
                <label for="url" class="block text-sm font-medium text-gray-300 mb-2">
                    WordPress URL <span class="text-red-400">*</span>
                </label>
                <input type="url" 
                       id="url" 
                       name="url" 
                       value="{{ old('url') }}"
                       class="w-full px-4 py-3 bg-[#1E1E1E] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-[#00ADB5] focus:ring-1 focus:ring-[#00ADB5] transition-colors"
                       placeholder="https://yoursite.com"
                       required>
                @error('url')
                    <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                @enderror
                <p class="text-gray-400 text-sm mt-1">Enter the full URL of your WordPress site</p>
            </div>

            <!-- Username -->
            <div class="mb-6">
                <label for="username" class="block text-sm font-medium text-gray-300 mb-2">
                    Username <span class="text-red-400">*</span>
                </label>
                <input type="text" 
                       id="username" 
                       name="username" 
                       value="{{ old('username') }}"
                       class="w-full px-4 py-3 bg-[#1E1E1E] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-[#00ADB5] focus:ring-1 focus:ring-[#00ADB5] transition-colors"
                       placeholder="WordPress username"
                       required>
                @error('username')
                    <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Password -->
            <div class="mb-6">
                <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                    Password <span class="text-red-400">*</span>
                </label>
                <div class="relative">
                    <input type="password" 
                           id="password" 
                           name="password"
                           class="w-full px-4 py-3 bg-[#1E1E1E] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-[#00ADB5] focus:ring-1 focus:ring-[#00ADB5] transition-colors pr-12"
                           placeholder="WordPress password"
                           required>
                    <button type="button" 
                            onclick="togglePassword()" 
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-eye" id="passwordToggleIcon"></i>
                    </button>
                </div>
                @error('password')
                    <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                @enderror
                <p class="text-gray-400 text-sm mt-1">Use your regular WordPress login password</p>
            </div>

            <!-- Set as Default -->
            <div class="mb-8">
                <label class="flex items-center">
                    <input type="checkbox" 
                           name="is_default" 
                           value="1"
                           {{ old('is_default') ? 'checked' : '' }}
                           class="w-4 h-4 text-[#00ADB5] bg-[#1E1E1E] border-gray-600 rounded focus:ring-[#00ADB5] focus:ring-2">
                    <span class="ml-2 text-gray-300">Set as default connection</span>
                </label>
                <p class="text-gray-400 text-sm mt-1">This connection will be used by default for new campaigns</p>
            </div>

            <!-- Test Connection Button -->
            <div class="mb-6">
                <button type="button" 
                        onclick="testConnection()" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-plug mr-2"></i>Test Connection
                </button>
                <div id="testResult" class="mt-3 hidden"></div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-4">
                <a href="{{ route('wordpress.connections.index') }}" 
                   class="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-[#333333] transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-[#00ADB5] hover:bg-[#00ADB5]/80 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                    <i class="fas fa-save mr-2"></i>Save Connection
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

function testConnection() {
    const button = event.target;
    const originalHtml = button.innerHTML;
    const resultDiv = document.getElementById('testResult');
    
    // Get form data
    const formData = {
        wordpress_url: document.getElementById('url').value,
        username: document.getElementById('username').value,
        password: document.getElementById('password').value
    };
    
    // Validate required fields
    if (!formData.wordpress_url || !formData.username || !formData.password) {
        showTestResult('Please fill in all required fields', 'error');
        return;
    }
    
    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';
    button.disabled = true;
    resultDiv.className = 'mt-3';
    resultDiv.innerHTML = '<div class="text-blue-400"><i class="fas fa-spinner fa-spin mr-2"></i>Testing connection...</div>';
    
    // Make test request
    fetch('/wordpress/connections/test-new', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showTestResult('✅ Connection successful! WordPress site is reachable.', 'success');
        } else {
            showTestResult('❌ Connection failed: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showTestResult('❌ Error testing connection: ' + error.message, 'error');
        console.error('Error:', error);
    })
    .finally(() => {
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}

function showTestResult(message, type) {
    const resultDiv = document.getElementById('testResult');
    const colorClass = type === 'success' ? 'text-green-400' : 'text-red-400';
    
    resultDiv.className = 'mt-3';
    resultDiv.innerHTML = `<div class="${colorClass}">${message}</div>`;
}
</script>
@endsection
