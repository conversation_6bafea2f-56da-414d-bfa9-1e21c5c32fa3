@extends('layouts.app')

@section('title', 'Campaigns')
@section('page-title', 'Campaigns')

@section('content')
<div class="space-y-6">
    <!-- Header with Create Button -->
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-xl font-semibold text-white">Manage Your Campaigns</h2>
            <p class="text-gray-400 mt-1">Create and manage landing page campaigns</p>
        </div>
        <a href="{{ route('campaigns.create') }}" 
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            New Campaign
        </a>
    </div>

    <!-- Campaigns List -->
    @if($campaigns->count() > 0)
        <div class="bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-medium text-white">All Campaigns</h3>
            </div>
            <div class="divide-y divide-gray-700">
                @foreach($campaigns as $campaign)
                <div class="p-6 hover:bg-gray-750 transition-colors duration-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center flex-1">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-cyan-500 rounded-lg flex items-center justify-center">
                                    <span class="text-sm font-medium text-white">{{ substr($campaign->name, 0, 2) }}</span>
                                </div>
                            </div>
                            <div class="ml-4 flex-1">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-medium text-white">
                                            <a href="{{ route('campaigns.show', $campaign) }}" class="hover:text-cyan-400">
                                                {{ $campaign->name }}
                                            </a>
                                        </h3>
                                        <p class="text-sm text-gray-400 mt-1">{{ Str::limit($campaign->description, 100) }}</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="flex items-center space-x-4 text-sm text-gray-400">
                                            <div>
                                                <span class="font-medium">{{ $campaign->generatedPages->count() }}</span>
                                                <span>Pages</span>
                                            </div>
                                            <div>
                                                <span class="font-medium">{{ $campaign->exportLogs->count() }}</span>
                                                <span>Exports</span>
                                            </div>
                                            <div>
                                                <span class="text-xs">{{ $campaign->created_at->format('M d, Y') }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center space-x-2 ml-4">
                            <!-- Status Badge -->
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($campaign->status === 'active') bg-green-800 text-green-100
                                @elseif($campaign->status === 'completed') bg-blue-800 text-blue-100
                                @elseif($campaign->status === 'draft') bg-yellow-800 text-yellow-100
                                @else bg-gray-700 text-gray-300 @endif">
                                {{ ucfirst($campaign->status) }}
                            </span>

                            <a href="{{ route('campaigns.show', $campaign) }}"
                               class="inline-flex items-center px-3 py-1 border border-gray-600 text-xs font-medium rounded text-gray-300 bg-gray-700 hover:bg-gray-600 transition-colors">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                View
                            </a>
                            <a href="{{ route('campaigns.edit', $campaign) }}"
                               class="inline-flex items-center px-3 py-1 border border-gray-600 text-xs font-medium rounded text-gray-300 bg-gray-700 hover:bg-gray-600 transition-colors">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Edit
                            </a>
                            @if($campaign->generatedPages->count() > 0)
                                <a href="{{ route('export.create', $campaign) }}"
                                   class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-cyan-600 hover:bg-cyan-700 transition-colors">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    Export
                                </a>
                            @endif
                            <form method="POST" action="{{ route('campaigns.destroy', $campaign) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this campaign? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="inline-flex items-center px-3 py-1 border border-red-600 text-xs font-medium rounded text-red-400 bg-red-900 hover:bg-red-800 transition-colors">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Pagination -->
        <div class="mt-6">
            {{ $campaigns->links() }}
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="mx-auto w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mb-4">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-white mb-2">No campaigns yet</h3>
            <p class="text-gray-400 mb-6">Get started by creating your first landing page campaign.</p>
            <a href="{{ route('campaigns.create') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Your First Campaign
            </a>
        </div>
    @endif
</div>

@push('styles')
<style>
    /* Custom pagination styles for dark theme */
    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        space-x: 1rem;
    }
    
    .pagination .page-link {
        color: #9CA3AF;
        background-color: #374151;
        border: 1px solid #4B5563;
        padding: 0.5rem 0.75rem;
        text-decoration: none;
        border-radius: 0.375rem;
        transition: all 0.2s;
    }
    
    .pagination .page-link:hover {
        color: #FFFFFF;
        background-color: #4B5563;
    }
    
    .pagination .page-item.active .page-link {
        color: #FFFFFF;
        background-color: #0891B2;
        border-color: #0891B2;
    }
    
    .pagination .page-item.disabled .page-link {
        color: #6B7280;
        background-color: #1F2937;
        border-color: #374151;
        cursor: not-allowed;
    }
</style>
@endpush
@endsection
