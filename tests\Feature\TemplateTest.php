<?php

use App\Models\Template;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

it('can display templates index page', function () {
    $response = $this->get('/templates');
    $response->assertSuccessful();
    $response->assertViewIs('templates.index');
});

it('can display create template page', function () {
    $response = $this->get('/templates/create');
    $response->assertSuccessful();
    $response->assertViewIs('templates.create');
});

it('can create a new template', function () {
    $templateData = [
        'name' => 'Test Template',
        'description' => 'Test description',
        'sections' => [
            [
                'name' => 'Hero Section',
                'html_content' => '<h1>Test Hero</h1><p>Test content</p>',
                'order' => 0
            ]
        ],
        'styles' => 'body { color: red; }',
        'scripts' => '<script src="test.js"></script>',
        'fonts' => '<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">',
        'is_active' => true,
        'sort_order' => 0
    ];

    $response = $this->post('/templates', $templateData);
    $response->assertRedirect();

    $this->assertDatabaseHas('templates', [
        'name' => 'Test Template',
        'description' => 'Test description'
    ]);
});

it('can show template details', function () {
    $template = Template::create([
        'name' => 'Test Template',
        'description' => 'Test description',
        'sections' => [
            [
                'name' => 'Hero Section',
                'html_content' => '<h1>Test Hero</h1><p>Test content</p>',
                'order' => 0
            ]
        ],
        'styles' => 'body { color: red; }',
        'scripts' => '<script src="test.js"></script>',
        'fonts' => '<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">',
        'is_active' => true,
        'sort_order' => 0
    ]);

    $response = $this->get("/templates/{$template->id}");
    $response->assertSuccessful();
    $response->assertViewIs('templates.show');
    $response->assertViewHas('template', $template);
});

it('can process template to generate placeholders', function () {
    $template = Template::create([
        'name' => 'Test Template',
        'description' => 'Test description',
        'sections' => [
            [
                'name' => 'Hero Section',
                'html_content' => '<h1>Test Hero Title</h1><p>Test hero content</p>',
                'order' => 0
            ]
        ],
        'is_processed' => false
    ]);

    $response = $this->post("/templates/{$template->id}/process");
    $response->assertSuccessful();

    $template->refresh();
    expect($template->is_processed)->toBeTrue();
    expect($template->placeholders)->not->toBeNull();
    expect($template->master_prompt)->not->toBeNull();
});

it('can preview template', function () {
    $template = Template::create([
        'name' => 'Test Template',
        'description' => 'Test description',
        'sections' => [
            [
                'name' => 'Hero Section',
                'html_content' => '<h1>Test Hero</h1><p>Test content</p>',
                'order' => 0
            ]
        ]
    ]);

    $response = $this->get("/templates/{$template->id}/preview");
    $response->assertSuccessful();
    $response->assertViewIs('templates.preview');
    $response->assertViewHas('template', $template);
});
