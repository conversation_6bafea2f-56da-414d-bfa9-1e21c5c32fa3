<?php

use App\Models\Campaign;
use App\Models\Template;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('can create campaign', function () {
    $campaignData = [
        'name' => 'Test Campaign',
        'business_name' => 'Test Business',
        'main_keyword' => 'test keyword',
        'business_description' => 'Test business description',
        'contact_phone' => '+***********',
        'contact_email' => '<EMAIL>',
        'address' => 'Test Address',
        'people_also_ask' => ['Question 1?', 'Question 2?'],
        'status' => 'active'
    ];

    $response = $this->post('/campaigns', $campaignData);

    $response->assertRedirect();
    $this->assertDatabaseHas('campaigns', [
        'name' => 'Test Campaign',
        'business_name' => 'Test Business',
        'main_keyword' => 'test keyword'
    ]);
});

test('can view campaigns', function () {
    Campaign::create([
        'name' => 'Test Campaign',
        'business_name' => 'Test Business',
        'main_keyword' => 'test keyword',
        'business_description' => 'Test description',
        'status' => 'active'
    ]);

    $response = $this->get('/campaigns');

    $response->assertStatus(200);
    $response->assertSee('Test Campaign');
});

test('can view single campaign', function () {
    $campaign = Campaign::create([
        'name' => 'Test Campaign',
        'business_name' => 'Test Business',
        'main_keyword' => 'test keyword',
        'business_description' => 'Test description',
        'status' => 'active'
    ]);

    $response = $this->get("/campaigns/{$campaign->id}");

    $response->assertStatus(200);
    $response->assertSee('Test Campaign');
});

test('can generate master prompt', function () {
    $campaign = Campaign::create([
        'name' => 'Test Campaign',
        'business_name' => 'Test Business',
        'main_keyword' => 'test service',
        'business_description' => 'We provide test services',
        'people_also_ask' => ['What is test service?', 'How much does it cost?'],
        'status' => 'active'
    ]);

    $response = $this->get("/content-generator/{$campaign->id}/master-prompt");

    $response->assertStatus(200);
    $response->assertJsonStructure(['prompt']);

    $data = $response->json();
    expect($data['prompt'])->toContain('Test Business');
    expect($data['prompt'])->toContain('test service');
});
