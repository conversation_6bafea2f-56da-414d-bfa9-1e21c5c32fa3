<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Campaign>
 */
class CampaignFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->sentence(3),
            'business_name' => $this->faker->company(),
            'main_keyword' => $this->faker->words(2, true),
            'business_description' => $this->faker->paragraph(),
            'contact_phone' => $this->faker->phoneNumber(),
            'contact_email' => $this->faker->companyEmail(),
            'address' => $this->faker->address(),
            'people_also_ask' => [
                $this->faker->sentence() . '?',
                $this->faker->sentence() . '?',
                $this->faker->sentence() . '?',
            ],
            'status' => $this->faker->randomElement(['draft', 'active', 'completed']),
            'master_prompt' => null,
            'campaign_data' => [],
        ];
    }
}
