@extends('layouts.app')

@section('title', $campaign->name)
@section('page-title', $campaign->name)

@section('content')
<div class="space-y-6">
    <!-- Campaign Header -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-cyan-500 rounded-lg flex items-center justify-center">
                            <span class="text-lg font-medium text-white">{{ substr($campaign->name, 0, 2) }}</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-2xl font-bold text-white">{{ $campaign->name }}</h1>
                        <p class="text-gray-400">{{ $campaign->description }}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                        @if($campaign->status === 'active') bg-green-800 text-green-100
                        @elseif($campaign->status === 'completed') bg-blue-800 text-blue-100
                        @elseif($campaign->status === 'draft') bg-yellow-800 text-yellow-100
                        @else bg-gray-700 text-gray-300 @endif">
                        {{ ucfirst($campaign->status) }}
                    </span>
                    <a href="{{ route('campaigns.edit', $campaign) }}" 
                       class="inline-flex items-center px-3 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit
                    </a>
                </div>
            </div>

            <!-- Campaign Stats -->
            <div class="grid grid-cols-1 gap-5 sm:grid-cols-4">
                <div class="bg-gray-700 px-4 py-3 rounded-lg">
                    <div class="text-2xl font-bold text-white">{{ $campaign->generatedPages->count() }}</div>
                    <div class="text-sm text-gray-400">Generated Pages</div>
                </div>
                <div class="bg-gray-700 px-4 py-3 rounded-lg">
                    <div class="text-2xl font-bold text-white">{{ $campaign->generatedPages->where('status', 'exported')->count() }}</div>
                    <div class="text-sm text-gray-400">Exported Pages</div>
                </div>
                <div class="bg-gray-700 px-4 py-3 rounded-lg">
                    <div class="text-2xl font-bold text-white">{{ $campaign->exportLogs->count() }}</div>
                    <div class="text-sm text-gray-400">Export Logs</div>
                </div>
                <div class="bg-gray-700 px-4 py-3 rounded-lg">
                    <div class="text-2xl font-bold text-white">{{ $campaign->exportLogs->where('status', 'completed')->count() }}</div>
                    <div class="text-sm text-gray-400">Completed Exports</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign Details -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- Business Information -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">Business Information</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-400">Business Name</dt>
                        <dd class="text-sm text-white">{{ $campaign->business_name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-400">Main Keyword</dt>
                        <dd class="text-sm text-white">{{ $campaign->main_keyword }}</dd>
                    </div>
                    @if($campaign->contact_phone)
                        <div>
                            <dt class="text-sm font-medium text-gray-400">Contact Phone</dt>
                            <dd class="text-sm text-white">{{ $campaign->contact_phone }}</dd>
                        </div>
                    @endif
                    @if($campaign->contact_email)
                        <div>
                            <dt class="text-sm font-medium text-gray-400">Contact Email</dt>
                            <dd class="text-sm text-white">{{ $campaign->contact_email }}</dd>
                        </div>
                    @endif
                    @if($campaign->address)
                        <div>
                            <dt class="text-sm font-medium text-gray-400">Address</dt>
                            <dd class="text-sm text-white">{{ $campaign->address }}</dd>
                        </div>
                    @endif
                    @if($campaign->business_description)
                        <div>
                            <dt class="text-sm font-medium text-gray-400">Business Description</dt>
                            <dd class="text-sm text-white">{{ $campaign->business_description }}</dd>
                        </div>
                    @endif
                </dl>
            </div>
        </div>

        <!-- FAQ Questions -->
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">FAQ Questions</h3>
                @if($campaign->people_also_ask && count($campaign->people_also_ask) > 0)
                    <ul class="space-y-2">
                        @foreach($campaign->people_also_ask as $question)
                            <li class="text-sm text-gray-300">• {{ $question }}</li>
                        @endforeach
                    </ul>
                @else
                    <p class="text-sm text-gray-400">No FAQ questions added yet.</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-white mb-4">Actions</h3>
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                <a href="{{ route('campaigns.master-prompt', $campaign) }}" 
                   class="inline-flex items-center justify-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                    View Master Prompt
                </a>

                <a href="{{ route('content-generator.upload', $campaign) }}" 
                   class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 12l2 2 4-4"></path>
                    </svg>
                    Upload JSON & Generate
                </a>

                @if($campaign->generatedPages->count() > 0)
                    <div class="flex items-center space-x-4">
                        @if($campaign->wordpressConnection)
                            <div class="text-sm text-gray-400">
                                Export to: <span class="text-cyan-400 font-medium">{{ $campaign->wordpressConnection->name }}</span>
                            </div>
                        @endif
                        <a href="{{ route('export.create', $campaign) }}"
                           class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            Export to WordPress
                        </a>
                    </div>
                @endif

                <a href="{{ route('campaigns.edit', $campaign) }}" 
                   class="inline-flex items-center justify-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Campaign
                </a>
            </div>
        </div>
    </div>

    <!-- Generated Pages -->
    @if($campaign->generatedPages->count() > 0)
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg leading-6 font-medium text-white">Generated Pages</h3>
                    <div class="flex space-x-2">
                        <button onclick="selectAllPages()" class="bg-gray-600 hover:bg-gray-500 text-white px-3 py-1 rounded text-sm">
                            Select All
                        </button>
                        <button onclick="exportSelectedPages()" class="bg-green-600 hover:bg-green-500 text-white px-3 py-1 rounded text-sm" disabled id="exportSelectedBtn">
                            Export Selected
                        </button>
                        <form method="POST" action="{{ route('wordpress.export.all', $campaign) }}" class="inline">
                            @csrf
                            <button type="submit" class="bg-green-700 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">
                                Export All to WordPress
                            </button>
                        </form>
                        <button onclick="deleteSelectedPages()" class="bg-red-600 hover:bg-red-500 text-white px-3 py-1 rounded text-sm" disabled id="deleteSelectedBtn">
                            Delete Selected
                        </button>
                        <form method="POST" action="{{ route('campaigns.delete-all-pages', $campaign) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete ALL generated pages? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-red-700 hover:bg-red-600 text-white px-3 py-1 rounded text-sm">
                                Delete All
                            </button>
                        </form>
                    </div>
                </div>
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-700">
                        <thead class="bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                    <input type="checkbox" id="selectAllCheckbox" onchange="toggleAllPages()" class="rounded border-gray-600 text-cyan-600 focus:ring-cyan-500">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Title</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Template</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Created</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-gray-800 divide-y divide-gray-700">
                            @foreach($campaign->generatedPages as $page)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" name="selected_pages[]" value="{{ $page->id }}" class="page-checkbox rounded border-gray-600 text-cyan-600 focus:ring-cyan-500" onchange="updateDeleteButton()">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @php
                                            $heroHeadline = $page->page_data['Hero_headline'] ?? $page->title;
                                            $displayTitle = Str::limit($heroHeadline, 50);
                                        @endphp
                                        <div class="text-sm font-medium text-white">{{ $displayTitle }}</div>
                                        <div class="text-sm text-gray-400">{{ $page->slug }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ $page->template->name }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($page->status === 'exported') bg-green-800 text-green-100
                                            @else bg-gray-700 text-gray-300 @endif">
                                            {{ ucfirst($page->status) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                                        {{ $page->created_at->diffForHumans() }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex items-center justify-end space-x-2">
                                            <a href="{{ route('content-generator.preview', $page) }}"
                                               class="text-cyan-400 hover:text-cyan-300">Preview</a>
                                            <a href="{{ route('content-generator.preview-window', $page) }}"
                                               target="_blank"
                                               class="text-gray-400 hover:text-white">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                            </a>
                                            <button onclick="exportSinglePage({{ $page->id }})" class="text-green-400 hover:text-green-300 ml-2" title="Export to WordPress">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                </svg>
                                            </button>
                                            <form method="POST" action="{{ route('campaigns.delete-page', [$campaign, $page]) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this page?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-400 hover:text-red-300 ml-2">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

    <!-- Export Logs -->
    @if($exportLogs->count() > 0)
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">Export History</h3>
                <div class="space-y-3">
                    @foreach($exportLogs as $log)
                        <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                            <div>
                                <div class="text-sm font-medium text-white">{{ $log->wordpress_site_url }}</div>
                                <div class="text-sm text-gray-400">
                                    {{ $log->exported_pages }}/{{ $log->total_pages }} pages • {{ $log->created_at->diffForHumans() }}
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    @if($log->status === 'completed') bg-green-800 text-green-100
                                    @elseif($log->status === 'failed') bg-red-800 text-red-100
                                    @elseif($log->status === 'processing') bg-yellow-800 text-yellow-100
                                    @else bg-gray-700 text-gray-300 @endif">
                                    {{ ucfirst($log->status) }}
                                </span>
                                <a href="{{ route('export.show', $log) }}" 
                                   class="text-cyan-400 hover:text-cyan-300 text-sm">View</a>
                            </div>
                        </div>
                    @endforeach
                </div>
                <div class="mt-4">
                    {{ $exportLogs->links() }}
                </div>
            </div>
        </div>
    @endif
</div>

<script>
function toggleAllPages() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const pageCheckboxes = document.querySelectorAll('.page-checkbox');

    pageCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateDeleteButton();
}

function selectAllPages() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    selectAllCheckbox.checked = true;
    toggleAllPages();
}

function updateDeleteButton() {
    const checkedBoxes = document.querySelectorAll('.page-checkbox:checked');
    const deleteBtn = document.getElementById('deleteSelectedBtn');
    const exportBtn = document.getElementById('exportSelectedBtn');

    if (checkedBoxes.length > 0) {
        deleteBtn.disabled = false;
        deleteBtn.textContent = `Delete Selected (${checkedBoxes.length})`;
        exportBtn.disabled = false;
        exportBtn.textContent = `Export Selected (${checkedBoxes.length})`;
    } else {
        deleteBtn.disabled = true;
        deleteBtn.textContent = 'Delete Selected';
        exportBtn.disabled = true;
        exportBtn.textContent = 'Export Selected';
    }
}

function deleteSelectedPages() {
    const checkedBoxes = document.querySelectorAll('.page-checkbox:checked');

    if (checkedBoxes.length === 0) {
        alert('Please select pages to delete.');
        return;
    }

    if (!confirm(`Are you sure you want to delete ${checkedBoxes.length} selected page(s)? This action cannot be undone.`)) {
        return;
    }

    const pageIds = Array.from(checkedBoxes).map(cb => cb.value);

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("campaigns.delete-selected-pages", $campaign) }}';

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    form.appendChild(csrfToken);

    // Add method override
    const methodField = document.createElement('input');
    methodField.type = 'hidden';
    methodField.name = '_method';
    methodField.value = 'DELETE';
    form.appendChild(methodField);

    // Add page IDs
    pageIds.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'page_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
}

function exportSelectedPages() {
    const checkedBoxes = document.querySelectorAll('.page-checkbox:checked');

    if (checkedBoxes.length === 0) {
        alert('Please select pages to export.');
        return;
    }

    const pageIds = Array.from(checkedBoxes).map(cb => cb.value);

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("wordpress.export.selected", $campaign) }}';

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    form.appendChild(csrfToken);

    // Add page IDs
    pageIds.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'page_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
}

function exportSinglePage(pageId) {
    // Redirect to export form with pre-selected page for better UX
    window.location.href = `{{ route('export.create', $campaign) }}?page_id=${pageId}`;
}
</script>
@endsection
