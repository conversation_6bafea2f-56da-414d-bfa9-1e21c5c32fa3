<?php

namespace App\Services;

use DOMDocument;
use DOMXPath;

class WordPressContentService
{
    /**
     * Prepare content for WordPress export with all necessary assets
     */
    public function prepareContentForWordPress(string $htmlContent): string
    {
        // Extract body content from full HTML
        $bodyContent = $this->extractBodyContent($htmlContent);

        // Add WordPress-compatible assets
        $wordpressContent = $this->addWordPressAssets($bodyContent);

        return $wordpressContent;
    }

    /**
     * Prepare complete HTML content for WordPress export with all necessary assets
     * Uses the complete Raw HTML from preview window which includes all CSS, JS, fonts
     * Returns the COMPLETE HTML including DOCTYPE, head, body, etc.
     */
    public function prepareCompleteContentForWordPress(\App\Models\GeneratedPage $page): string
    {
        // Get the complete HTML from preview window (same as "View HTML" modal)
        // This is exactly what user sees in preview and what they want exported
        return $this->getCompletePreviewHtml($page);
    }

    /**
     * Get complete HTML as shown in preview window (same as "View HTML" modal)
     * This includes all CSS, JS, fonts, and assets embedded
     */
    public function getCompletePreviewHtml(\App\Models\GeneratedPage $page): string
    {
        // Return the complete HTML with all assets embedded
        // This is the same HTML that user sees in "View HTML" modal
        // Use raw_html which contains the complete HTML with DOCTYPE, head, body, etc.
        return $page->raw_html;
    }

    /**
     * Prepare shortcode content for WordPress export with all necessary assets
     */
    public function prepareShortcodeContentForWordPress(string $shortcodeContent): string
    {
        // Add WordPress-compatible assets directly to shortcode content
        $wordpressContent = $this->addWordPressAssets($shortcodeContent);

        return $wordpressContent;
    }

    /**
     * Extract assets (CSS, JS, Fonts) from complete HTML
     */
    public function extractAssetsFromHtml(string $htmlContent): array
    {
        $css = '';
        $js = '';

        // Extract CSS from <style> tags and <link> tags
        if (preg_match_all('/<style[^>]*>(.*?)<\/style>/is', $htmlContent, $styleMatches)) {
            foreach ($styleMatches[1] as $styleContent) {
                $css .= "<style>\n" . $styleContent . "\n</style>\n";
            }
        }

        if (preg_match_all('/<link[^>]*rel=["\']stylesheet["\'][^>]*>/i', $htmlContent, $linkMatches)) {
            foreach ($linkMatches[0] as $linkTag) {
                $css .= $linkTag . "\n";
            }
        }

        // Extract JavaScript from <script> tags
        if (preg_match_all('/<script[^>]*>(.*?)<\/script>/is', $htmlContent, $scriptMatches)) {
            foreach ($scriptMatches[0] as $scriptTag) {
                $js .= $scriptTag . "\n";
            }
        }

        // Extract external script tags
        if (preg_match_all('/<script[^>]*src=[^>]*><\/script>/i', $htmlContent, $externalScriptMatches)) {
            foreach ($externalScriptMatches[0] as $scriptTag) {
                $js .= $scriptTag . "\n";
            }
        }

        return [
            'css' => $css,
            'js' => $js
        ];
    }

    /**
     * Extract body content from full HTML document
     */
    private function extractBodyContent(string $htmlContent): string
    {
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML('<?xml encoding="UTF-8">' . $htmlContent, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        libxml_clear_errors();

        $xpath = new DOMXPath($dom);
        $bodyNodes = $xpath->query('//body');

        if ($bodyNodes->length > 0) {
            $bodyContent = '';
            foreach ($bodyNodes->item(0)->childNodes as $child) {
                $bodyContent .= $dom->saveHTML($child);
            }
            return $bodyContent;
        }

        return $htmlContent;
    }

    /**
     * Add WordPress-compatible assets (CSS, JS, Fonts)
     */
    private function addWordPressAssets(string $bodyContent): string
    {
        $assets = $this->getWordPressAssets();
        
        return $assets['css'] . $bodyContent . $assets['js'];
    }

    /**
     * Get all necessary assets for WordPress
     */
    private function getWordPressAssets(): array
    {
        return [
            'css' => $this->getWordPressCss(),
            'js' => $this->getWordPressJs()
        ];
    }

    /**
     * Get WordPress-compatible CSS
     */
    private function getWordPressCss(): string
    {
        return '
<!-- Tailwind CSS -->
<script src="https://cdn.tailwindcss.com"></script>

<!-- Google Fonts: Nunito -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&display=swap" rel="stylesheet">

<!-- Alpine.js for interactive components -->
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

<!-- WordPress Compatible Styles -->
<style>
/* General Styles */
body {
    font-family: "Nunito", sans-serif !important;
    --primary-color: #3b82f6; /* blue-500 */
    --secondary-color: #10b981; /* emerald-500 */
    --text-color: #1f2937; /* gray-800 */
    --bg-color: #ffffff;
    color: var(--text-color);
    scroll-behavior: smooth;
}

/* Blob Animation */
@keyframes blob {
    0% {
        transform: scale(1) translate(0px, 0px);
    }
    33% {
        transform: scale(1.1) translate(30px, -50px);
    }
    66% {
        transform: scale(0.9) translate(-20px, 20px);
    }
    100% {
        transform: scale(1) translate(0px, 0px);
    }
}
.animate-blob {
    animation: blob 7s infinite alternate;
}
.animation-delay-2000 {
    animation-delay: 2s;
}
.animation-delay-4000 {
    animation-delay: 4s;
}

/* Button Styles */
.lp-btn {
    background-color: var(--primary-color);
    color: white;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 1.125rem; /* 18px */
    font-weight: 700;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px 0 rgba(59, 130, 246, 0.35);
}
.lp-btn:hover {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: 0 7px 20px 0 rgba(59, 130, 246, 0.4);
}
.lp-btn-secondary {
    background-color: var(--secondary-color);
    box-shadow: 0 4px 15px 0 rgba(16, 185, 129, 0.35);
}
.lp-btn-secondary:hover {
    box-shadow: 0 7px 20px 0 rgba(16, 185, 129, 0.4);
}

/* Section Styles */
.lp-section {
    padding: 4rem 1.5rem;
}
@media (min-width: 768px) {
    .lp-section {
        padding: 5rem 2rem;
    }
}

/* FAQ Styles */
.faq-item {
    border-bottom: 1px solid #e5e7eb; /* gray-200 */
}
.faq-item summary {
    cursor: pointer;
    outline: none;
}
.faq-item[open] summary svg:last-child {
    transform: rotate(180deg);
}

/* Prose styles for blog section */
.prose h3 {
    font-weight: 700;
    margin-top: 2em;
    margin-bottom: 1em;
}
.prose ul {
    list-style-type: disc;
    padding-left: 1.5em;
}
.prose table {
    width: 100%;
    margin-top: 1.5em;
    margin-bottom: 1.5em;
    border-collapse: collapse;
}
.prose th, .prose td {
    border: 1px solid #d1d5db;
    padding: 0.75em 1em;
}
.prose th {
    font-weight: 700;
    background-color: #f9fafb;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Nunito", sans-serif !important;
    line-height: 1.6;
    color: #1f2937;
    background-color: #ffffff;
    scroll-behavior: smooth;
}

/* WordPress Content Container */
.wp-content-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
}

/* Tailwind-like Utility Classes */
.container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }

/* Typography */
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.text-center { text-align: center; }
.text-left { text-align: left; }

/* Colors */
.text-white { color: #ffffff; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-blue-600 { color: #2563eb; }
.text-green-600 { color: #059669; }
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-blue-600 { background-color: #2563eb; }
.bg-green-600 { background-color: #059669; }
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }

/* Layout */
.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

/* Grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

/* Spacing */
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }
.max-w-7xl { max-width: 80rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-4xl { max-width: 56rem; }

/* Borders and Shadows */
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.border { border-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s;
    cursor: pointer;
    border: none;
}

.btn-primary {
    background-color: #2563eb;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #1d4ed8;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #059669;
    color: #ffffff;
}

.btn-secondary:hover {
    background-color: #047857;
    transform: translateY(-1px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 5rem 0;
    text-align: center;
}

/* Feature Cards */
.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

/* Pricing Cards */
.pricing-card {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.2s;
}

.pricing-card.featured {
    border-color: #2563eb;
    transform: scale(1.05);
}

.pricing-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* FAQ Section */
.faq-item {
    border-bottom: 1px solid #e5e7eb;
    padding: 1.5rem 0;
}

.faq-question {
    font-weight: 600;
    color: #1f2937;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faq-answer {
    margin-top: 1rem;
    color: #4b5563;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container { padding: 0 1rem; }
    .text-6xl { font-size: 2.5rem; }
    .text-5xl { font-size: 2rem; }
    .text-4xl { font-size: 1.875rem; }
    .py-20 { padding-top: 3rem; padding-bottom: 3rem; }
    .py-16 { padding-top: 2.5rem; padding-bottom: 2.5rem; }
    .grid-cols-3 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
    .grid-cols-2 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
    .hero-section { padding: 3rem 0; }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .grid-cols-3 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* WordPress Specific Fixes */
.wp-content-container img {
    max-width: 100%;
    height: auto;
}

.wp-content-container a {
    color: #2563eb;
    text-decoration: none;
}

.wp-content-container a:hover {
    text-decoration: underline;
}

/* Ensure content doesn\'t break WordPress theme */
.wp-content-container * {
    box-sizing: border-box;
}
</style>
';
    }

    /**
     * Get WordPress-compatible JavaScript
     */
    private function getWordPressJs(): string
    {
        return '
<!-- WordPress Compatible Scripts -->
<script>
document.addEventListener("DOMContentLoaded", function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll(\'a[href^="#"]\').forEach(anchor => {
        anchor.addEventListener("click", function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute("href"));
            if (target) {
                target.scrollIntoView({
                    behavior: "smooth",
                    block: "start"
                });
            }
        });
    });

    // FAQ Toggle Functionality
    document.querySelectorAll(".faq-question").forEach(question => {
        question.addEventListener("click", function() {
            const answer = this.nextElementSibling;
            const icon = this.querySelector(".faq-icon");
            
            if (answer.style.display === "none" || answer.style.display === "") {
                answer.style.display = "block";
                if (icon) icon.textContent = "−";
            } else {
                answer.style.display = "none";
                if (icon) icon.textContent = "+";
            }
        });
    });

    // Add animation classes on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px"
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add("animate-fade-in-up");
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll(".feature-card, .pricing-card, .faq-item").forEach(el => {
        observer.observe(el);
    });

    // Form handling (if any forms exist)
    document.querySelectorAll("form").forEach(form => {
        form.addEventListener("submit", function(e) {
            const submitBtn = form.querySelector(\'button[type="submit"]\');
            if (submitBtn) {
                submitBtn.textContent = "Mengirim...";
                submitBtn.disabled = true;
            }
        });
    });

    // Mobile menu toggle (if exists)
    const mobileMenuBtn = document.querySelector(".mobile-menu-btn");
    const mobileMenu = document.querySelector(".mobile-menu");
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener("click", function() {
            mobileMenu.classList.toggle("hidden");
        });
    }

    console.log("WordPress content scripts loaded successfully");
});
</script>
';
    }
}
