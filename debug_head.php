<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

$posts = get_posts(array(
    'post_type' => 'landing_page',
    'post_name' => 'pembuatan-website-ukm-startup',
    'post_status' => 'publish',
    'numberposts' => 1
));

if (!empty($posts)) {
    $post = $posts[0];
    echo 'Post ID: ' . $post->ID . PHP_EOL;
    echo 'Post Name: ' . $post->post_name . PHP_EOL;
    echo 'Post Title: ' . $post->post_title . PHP_EOL;
    
    $head_content = get_post_meta($post->ID, 'page_generator_head_content', true);
    
    echo 'Head content length: ' . strlen($head_content) . PHP_EOL;
    echo 'Head content preview (first 500 chars):' . PHP_EOL;
    echo substr($head_content, 0, 500) . PHP_EOL;
    echo '...' . PHP_EOL;
    
    // Check for placeholders
    if (strpos($head_content, '{{Yoast_title}}') !== false) {
        echo 'Contains {{Yoast_title}} placeholder' . PHP_EOL;
    }
    if (strpos($head_content, '{{Yoast_description}}') !== false) {
        echo 'Contains {{Yoast_description}} placeholder' . PHP_EOL;
    }
    if (strpos($head_content, '{{Yoast_focus_keyword}}') !== false) {
        echo 'Contains {{Yoast_focus_keyword}} placeholder' . PHP_EOL;
    }
} else {
    echo 'Post not found' . PHP_EOL;
}
