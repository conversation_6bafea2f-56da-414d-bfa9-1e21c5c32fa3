/**
 * Page Generator Integration - Admin JavaScript (Vanilla JS)
 */

(function() {
    'use strict';

    // Initialize when document is ready
    document.addEventListener('DOMContentLoaded', function() {
        initializePageGenerator();
    });

    function initializePageGenerator() {
        // Initialize components
        initSectionManagement();
        initAPITesting();
        initFormValidation();
        initTooltips();

        // Auto-refresh activity every 30 seconds
        setInterval(refreshActivity, 30000);
    }

    function initSectionManagement() {
        // Generate shortcode functionality
        const generateBtn = document.getElementById('generate-shortcode');
        if (generateBtn) {
            generateBtn.addEventListener('click', function(e) {
                e.preventDefault();

                const sectionName = document.getElementById('section-name').value.trim();
                if (!sectionName) {
                    alert('Please enter a section name first.');
                    return;
                }

                // Generate shortcode from section name
                const shortcode = generateShortcodeFromName(sectionName);
                document.getElementById('section-shortcode').value = shortcode;
            });
        }

        // Save section form
        const sectionForm = document.getElementById('section-form');
        if (sectionForm) {
            sectionForm.addEventListener('submit', function(e) {
                e.preventDefault();
                saveSectionForm();
            });
        }

        // Edit section functionality
        const editButtons = document.querySelectorAll('.edit-section');
        editButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const sectionId = this.getAttribute('data-id');
                loadSectionForEdit(sectionId);
            });
        });

        // Delete section with confirmation
        const deleteButtons = document.querySelectorAll('.delete-section');
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const sectionId = this.getAttribute('data-id');
                const row = this.closest('tr');
                const sectionName = row.querySelector('td:first-child strong').textContent;

                if (confirm(`Are you sure you want to delete "${sectionName}"? This action cannot be undone.`)) {
                    deleteSection(sectionId);
                }
            });
        });

        // Copy shortcode to clipboard
        document.addEventListener('click', function(e) {
            if (e.target.id === 'shortcode-preview') {
                e.preventDefault();

                const shortcode = e.target.textContent;
                copyToClipboard(shortcode);

                showNotice('Shortcode copied to clipboard!', 'success');
            }
        });
    }

    // Helper function to generate shortcode from section name
    function generateShortcodeFromName(name) {
        return name.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '') // Remove special characters
            .replace(/\s+/g, '_') // Replace spaces with underscores
            .substring(0, 20); // Limit length
    }

    // Save section form via AJAX
    function saveSectionForm() {
        const formData = new FormData();
        formData.append('action', 'save_custom_section');
        formData.append('nonce', pgAdmin.nonce);
        formData.append('name', document.getElementById('section-name').value);
        formData.append('shortcode', document.getElementById('section-shortcode').value);
        formData.append('description', document.getElementById('section-description').value);
        formData.append('html_content', document.getElementById('section-html').value);
        formData.append('css_content', document.getElementById('section-css').value);
        formData.append('js_content', document.getElementById('section-js').value);

        const submitBtn = document.querySelector('#section-form button[type="submit"]');
        const originalText = submitBtn.textContent;

        // Disable button and show loading
        submitBtn.disabled = true;
        submitBtn.textContent = 'Saving...';

        fetch(pgAdmin.ajaxurl, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotice('Section saved successfully!', 'success');

                // Show shortcode preview
                const shortcodePreview = document.getElementById('shortcode-preview');
                const shortcodeDisplay = document.querySelector('.pg-shortcode-display');

                if (shortcodePreview && shortcodeDisplay) {
                    shortcodePreview.textContent = '[' + document.getElementById('section-shortcode').value + ']';
                    shortcodeDisplay.classList.remove('pg-hidden');
                }

                // Reset form
                document.getElementById('section-form').reset();

                // Reload sections table after a short delay
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showNotice(data.data || 'Failed to save section.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotice('An error occurred while saving the section.', 'error');
        })
        .finally(() => {
            // Re-enable button
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    }

    function showEditModal(shortcode, currentContent) {
        // Create modal HTML
        const modalHTML = `
            <div id="edit-section-modal" class="page-generator-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Edit Section: ${shortcode}</h3>
                        <button type="button" class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="edit-section-form">
                            <div class="form-group">
                                <label for="section-content">HTML Content:</label>
                                <textarea id="section-content" name="content" class="code-editor" rows="15">${currentContent}</textarea>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="validate-html" checked>
                                    Validate HTML before saving
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="button button-secondary modal-cancel">Cancel</button>
                        <button type="button" class="button button-primary save-section" data-shortcode="${shortcode}">Save Changes</button>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        $('body').append(modalHTML);
        
        // Show modal
        $('#edit-section-modal').fadeIn();
        
        // Focus on textarea
        $('#section-content').focus();
        
        // Bind modal events
        bindModalEvents();
    }

    function bindModalEvents() {
        // Close modal
        $('.modal-close, .modal-cancel').on('click', function() {
            closeModal();
        });

        // Close on background click
        $('.page-generator-modal').on('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Save section
        $('.save-section').on('click', function() {
            const shortcode = $(this).data('shortcode');
            const content = $('#section-content').val();
            const validateHTML = $('#validate-html').is(':checked');
            
            saveSectionContent(shortcode, content, validateHTML);
        });

        // ESC key to close
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27) {
                closeModal();
            }
        });
    }

    function closeModal() {
        $('#edit-section-modal').fadeOut(function() {
            $(this).remove();
        });
        
        // Unbind ESC key
        $(document).off('keydown');
    }

    function saveSectionContent(shortcode, content, validateHTML) {
        // Show loading
        $('.save-section').prop('disabled', true).text('Saving...');
        
        // Validate HTML if requested
        if (validateHTML && !isValidHTML(content)) {
            showNotice('Invalid HTML detected. Please check your markup.', 'error');
            $('.save-section').prop('disabled', false).text('Save Changes');
            return;
        }

        // AJAX save
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'save_static_section',
                shortcode: shortcode,
                content: content,
                _wpnonce: pageGeneratorAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice('Section updated successfully!', 'success');
                    closeModal();
                    
                    // Refresh the page to show updated content
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showNotice('Error saving section: ' + response.data, 'error');
                }
            },
            error: function() {
                showNotice('Network error. Please try again.', 'error');
            },
            complete: function() {
                $('.save-section').prop('disabled', false).text('Save Changes');
            }
        });
    }

    function initAPITesting() {
        // Test API connection
        $('#test-api-connection').on('click', function(e) {
            e.preventDefault();
            
            const $button = $(this);
            $button.prop('disabled', true).text('Testing...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'test_api_connection',
                    _wpnonce: pageGeneratorAdmin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotice('API connection successful!', 'success');
                        updateConnectionStatus(true);
                    } else {
                        showNotice('API connection failed: ' + response.data, 'error');
                        updateConnectionStatus(false);
                    }
                },
                error: function() {
                    showNotice('Network error during API test.', 'error');
                    updateConnectionStatus(false);
                },
                complete: function() {
                    $button.prop('disabled', false).text('Test Connection');
                }
            });
        });
    }

    function updateConnectionStatus(isOnline) {
        const $indicator = $('.status-indicator');
        const $status = $('.connection-status');
        
        if (isOnline) {
            $indicator.removeClass('offline').addClass('online');
            $status.text('Connected');
        } else {
            $indicator.removeClass('online').addClass('offline');
            $status.text('Disconnected');
        }
    }

    function initFormValidation() {
        // Validate shortcode name
        $('input[name="shortcode_name"]').on('input', function() {
            const value = $(this).val();
            const isValid = /^[a-z0-9_]+$/.test(value);
            
            if (!isValid && value.length > 0) {
                $(this).addClass('invalid');
                showFieldError($(this), 'Only lowercase letters, numbers, and underscores allowed');
            } else {
                $(this).removeClass('invalid');
                hideFieldError($(this));
            }
        });

        // Validate HTML content
        $('textarea[name="html_content"]').on('blur', function() {
            const content = $(this).val();
            
            if (content && !isValidHTML(content)) {
                $(this).addClass('invalid');
                showFieldError($(this), 'HTML appears to be invalid. Please check your markup.');
            } else {
                $(this).removeClass('invalid');
                hideFieldError($(this));
            }
        });
    }

    function initTooltips() {
        // Simple tooltip implementation
        $('[data-tooltip]').hover(
            function() {
                const tooltip = $('<div class="page-generator-tooltip">' + $(this).data('tooltip') + '</div>');
                $('body').append(tooltip);
                
                const $this = $(this);
                const offset = $this.offset();
                
                tooltip.css({
                    top: offset.top - tooltip.outerHeight() - 10,
                    left: offset.left + ($this.outerWidth() / 2) - (tooltip.outerWidth() / 2)
                }).fadeIn();
            },
            function() {
                $('.page-generator-tooltip').remove();
            }
        );
    }

    function refreshActivity() {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_recent_activity',
                _wpnonce: pageGeneratorAdmin.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    updateActivityList(response.data);
                }
            }
        });
    }

    function updateActivityList(activities) {
        const $list = $('.activity-list');
        $list.empty();
        
        if (activities.length === 0) {
            $list.append('<li class="no-activity">No recent activity</li>');
            return;
        }
        
        activities.forEach(function(activity) {
            const $item = $(`
                <li>
                    <span class="activity-message">${activity.message}</span>
                    <span class="activity-time">${activity.time}</span>
                </li>
            `);
            $list.append($item);
        });
    }

    // Utility functions
    function isValidHTML(html) {
        try {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            return doc.getElementsByTagName('parsererror').length === 0;
        } catch (e) {
            return false;
        }
    }

    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text);
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    }

    function showNotice(message, type = 'info') {
        const noticeClass = `notice-${type}`;
        const $notice = $(`
            <div class="notice ${noticeClass} is-dismissible page-generator-notice">
                <p>${message}</p>
                <button type="button" class="notice-dismiss">
                    <span class="screen-reader-text">Dismiss this notice.</span>
                </button>
            </div>
        `);
        
        $('.wrap h1').after($notice);
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $notice.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
        
        // Manual dismiss
        $notice.find('.notice-dismiss').on('click', function() {
            $notice.fadeOut(function() {
                $(this).remove();
            });
        });
    }

    function showFieldError($field, message) {
        hideFieldError($field);
        
        const $error = $(`<div class="field-error">${message}</div>`);
        $field.after($error);
    }

    function hideFieldError($field) {
        $field.siblings('.field-error').remove();
    }

    // Export functions for global access
    window.pageGeneratorAdmin = {
        showNotice: showNotice,
        copyToClipboard: copyToClipboard,
        refreshActivity: refreshActivity
    };

})(jQuery);

// CSS for modal and notifications (injected via JavaScript)
const additionalCSS = `
<style>
.page-generator-modal {
    display: none;
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #1d2327;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #000;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    text-align: right;
}

.modal-footer .button {
    margin-left: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #1d2327;
}

.field-error {
    color: #d63638;
    font-size: 12px;
    margin-top: 5px;
}

.invalid {
    border-color: #d63638 !important;
    box-shadow: 0 0 0 1px #d63638 !important;
}

.page-generator-tooltip {
    position: absolute;
    background: #1d2327;
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10000;
    max-width: 200px;
    word-wrap: break-word;
}

.page-generator-notice {
    margin: 20px 0;
}

.no-activity {
    text-align: center;
    color: #646970;
    font-style: italic;
    padding: 20px;
}
</style>
`;

// Inject additional CSS
document.head.insertAdjacentHTML('beforeend', additionalCSS);
