<?php $__env->startSection('title', 'Edit WordPress Connection'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-[#0A0A0A] text-white">
    <div class="container mx-auto px-6 py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center space-x-4">
                <a href="<?php echo e(route('wordpress.connections.index')); ?>" 
                   class="text-gray-400 hover:text-white transition-colors">
                    <i class="fas fa-arrow-left text-xl"></i>
                </a>
                <h1 class="text-3xl font-bold">Edit WordPress Connection</h1>
            </div>
            <p class="text-gray-400">Update your WordPress site connection details</p>
        </div>

        <!-- Form -->
        <div class="max-w-2xl">
            <form action="<?php echo e(route('wordpress.connections.update', $connection)); ?>" method="POST" class="space-y-6">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <!-- Connection Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-300 mb-2">
                        Connection Name *
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="<?php echo e(old('name', $connection->name)); ?>"
                           class="w-full px-4 py-3 bg-[#1E1E1E] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-[#00ADB5] focus:ring-1 focus:ring-[#00ADB5] transition-colors"
                           placeholder="e.g., My WordPress Site"
                           required>
                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- WordPress URL -->
                <div>
                    <label for="url" class="block text-sm font-medium text-gray-300 mb-2">
                        WordPress URL *
                    </label>
                    <input type="url" 
                           id="url" 
                           name="url" 
                           value="<?php echo e(old('url', $connection->url)); ?>"
                           class="w-full px-4 py-3 bg-[#1E1E1E] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-[#00ADB5] focus:ring-1 focus:ring-[#00ADB5] transition-colors"
                           placeholder="https://yoursite.com"
                           required>
                    <p class="text-gray-400 text-sm mt-1">Enter the full URL of your WordPress site</p>
                    <?php $__errorArgs = ['url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Username -->
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-300 mb-2">
                        Username *
                    </label>
                    <input type="text" 
                           id="username" 
                           name="username" 
                           value="<?php echo e(old('username', $connection->username)); ?>"
                           class="w-full px-4 py-3 bg-[#1E1E1E] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-[#00ADB5] focus:ring-1 focus:ring-[#00ADB5] transition-colors"
                           placeholder="WordPress username"
                           required>
                    <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Password -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                        Password
                    </label>
                    <div class="relative">
                        <input type="password" 
                               id="password" 
                               name="password"
                               class="w-full px-4 py-3 bg-[#1E1E1E] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-[#00ADB5] focus:ring-1 focus:ring-[#00ADB5] transition-colors pr-12"
                               placeholder="Leave blank to keep current password">
                        <button type="button" 
                                onclick="togglePassword()" 
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                    <p class="text-gray-400 text-sm mt-1">Leave blank to keep current password</p>
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="text-red-400 text-sm mt-1"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Settings -->
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="is_default" 
                               name="is_default" 
                               value="1"
                               <?php echo e(old('is_default', $connection->is_default) ? 'checked' : ''); ?>

                               class="w-4 h-4 text-[#00ADB5] bg-[#1E1E1E] border-gray-600 rounded focus:ring-[#00ADB5] focus:ring-2">
                        <label for="is_default" class="ml-3 text-sm text-gray-300">
                            <span class="font-medium">Set as default connection</span>
                            <span class="ml-2 text-gray-300">Set as default connection</span>
                        </label>
                    </div>
                    <p class="text-gray-400 text-sm mt-1">This connection will be used by default for new campaigns</p>

                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="is_active" 
                               name="is_active" 
                               value="1"
                               <?php echo e(old('is_active', $connection->is_active) ? 'checked' : ''); ?>

                               class="w-4 h-4 text-[#00ADB5] bg-[#1E1E1E] border-gray-600 rounded focus:ring-[#00ADB5] focus:ring-2">
                        <label for="is_active" class="ml-3 text-sm text-gray-300">
                            <span class="font-medium">Active connection</span>
                        </label>
                    </div>
                    <p class="text-gray-400 text-sm mt-1">Inactive connections cannot be used for exports</p>
                </div>

                <!-- Test Connection Button -->
                <div class="mb-6">
                    <button type="button" 
                            onclick="testConnection(<?php echo e($connection->id); ?>)" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-plug mr-2"></i>Test Connection
                    </button>
                    <div id="testResult" class="mt-3 hidden"></div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-4">
                    <a href="<?php echo e(route('wordpress.connections.index')); ?>" 
                       class="px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-[#333333] transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-[#00ADB5] hover:bg-[#00ADB5]/80 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-save mr-2"></i>Update Connection
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

function testConnection(connectionId) {
    const button = event.target;
    const originalHtml = button.innerHTML;
    const resultDiv = document.getElementById('testResult');
    
    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';
    button.disabled = true;
    resultDiv.className = 'mt-3';
    resultDiv.innerHTML = '<div class="text-blue-400"><i class="fas fa-spinner fa-spin mr-2"></i>Testing connection...</div>';
    
    // Make test request
    fetch(`/wordpress/connections/${connectionId}/test`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showTestResult('✅ Connection successful! WordPress site is reachable.', 'success');
        } else {
            showTestResult('❌ Connection failed: ' + data.error, 'error');
        }
    })
    .catch(error => {
        showTestResult('❌ Error testing connection: ' + error.message, 'error');
        console.error('Error:', error);
    })
    .finally(() => {
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}

function showTestResult(message, type) {
    const resultDiv = document.getElementById('testResult');
    const bgColor = type === 'success' ? 'bg-green-900/20 border-green-500' : 'bg-red-900/20 border-red-500';
    const textColor = type === 'success' ? 'text-green-400' : 'text-red-400';
    
    resultDiv.className = `mt-3 p-4 rounded-lg border ${bgColor}`;
    resultDiv.innerHTML = `<div class="${textColor}">${message}</div>`;
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Herd\page-gent\resources\views/wordpress/connections/edit.blade.php ENDPATH**/ ?>