<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('sections'); // Array of sections with name, html_content, order
            $table->longText('styles')->nullable(); // Custom CSS
            $table->longText('scripts')->nullable(); // JS/Tailwind links
            $table->longText('fonts')->nullable(); // Google Fonts links
            $table->longText('master_prompt')->nullable(); // AI prompt template
            $table->json('placeholders')->nullable(); // Auto-generated placeholders
            $table->boolean('is_processed')->default(false); // Processing status
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('templates');
    }
};
