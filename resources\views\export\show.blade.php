@extends('layouts.app')

@section('title', 'Export Results - ' . $exportLog->campaign->name)

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-white">Export Results</h1>
        <p class="mt-2 text-gray-400">Campaign: {{ $exportLog->campaign->name }}</p>
    </div>

    <!-- Export Summary -->
    <div class="bg-gray-800 shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-cyan-500 rounded-lg flex items-center justify-center mr-4">
                        <span class="text-sm font-medium text-white">{{ substr($exportLog->campaign->name, 0, 2) }}</span>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-white">{{ $exportLog->campaign->name }}</h3>
                        <p class="text-sm text-gray-400">{{ $exportLog->wordpress_site_url }}</p>
                    </div>
                </div>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                    @if($exportLog->status === 'completed') bg-green-800 text-green-100
                    @elseif($exportLog->status === 'failed') bg-red-800 text-red-100
                    @else bg-yellow-800 text-yellow-100 @endif">
                    {{ ucfirst($exportLog->status) }}
                </span>
            </div>

            <!-- Progress Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="text-2xl font-bold text-white">{{ $exportLog->total_pages }}</div>
                    <div class="text-sm text-gray-400">Total Pages</div>
                </div>
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="text-2xl font-bold text-green-400">{{ $exportLog->exported_pages }}</div>
                    <div class="text-sm text-gray-400">Exported</div>
                </div>
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="text-2xl font-bold text-red-400">{{ $exportLog->failed_pages }}</div>
                    <div class="text-sm text-gray-400">Failed</div>
                </div>
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="text-2xl font-bold text-cyan-400">{{ $exportLog->getProgressPercentage() }}%</div>
                    <div class="text-sm text-gray-400">Progress</div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="mt-6">
                <div class="flex justify-between text-sm text-gray-400 mb-2">
                    <span>Export Progress</span>
                    <span>{{ $exportLog->exported_pages }}/{{ $exportLog->total_pages }}</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2">
                    <div class="bg-cyan-500 h-2 rounded-full" style="width: {{ $exportLog->getProgressPercentage() }}%"></div>
                </div>
            </div>

            @if($exportLog->error_message)
            <div class="mt-6 bg-red-800 border border-red-700 text-red-100 px-4 py-3 rounded">
                <h4 class="font-medium">Error Message:</h4>
                <p class="mt-1">{{ $exportLog->error_message }}</p>
            </div>
            @endif
        </div>
    </div>

    <!-- Export Settings -->
    <div class="bg-gray-800 shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-white mb-4">Export Settings</h3>
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div>
                    <dt class="text-sm font-medium text-gray-400">WordPress URL</dt>
                    <dd class="mt-1 text-sm text-white">{{ $exportLog->export_settings['wordpress_url'] ?? 'N/A' }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-400">Username</dt>
                    <dd class="mt-1 text-sm text-white">{{ $exportLog->export_settings['username'] ?? 'N/A' }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-400">Post Status</dt>
                    <dd class="mt-1 text-sm text-white">{{ ucfirst($exportLog->export_settings['post_status'] ?? 'draft') }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-400">Category</dt>
                    <dd class="mt-1 text-sm text-white">{{ isset($exportLog->export_settings['category_id']) && $exportLog->export_settings['category_id'] ? 'Category ID: ' . $exportLog->export_settings['category_id'] : 'None' }}</dd>
                </div>
            </dl>
        </div>
    </div>

    <!-- Timestamps -->
    <div class="bg-gray-800 shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-white mb-4">Timeline</h3>
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-3">
                <div>
                    <dt class="text-sm font-medium text-gray-400">Created</dt>
                    <dd class="mt-1 text-sm text-white">{{ $exportLog->created_at->format('M d, Y H:i') }}</dd>
                </div>
                @if($exportLog->started_at)
                <div>
                    <dt class="text-sm font-medium text-gray-400">Started</dt>
                    <dd class="mt-1 text-sm text-white">{{ $exportLog->started_at->format('M d, Y H:i') }}</dd>
                </div>
                @endif
                @if($exportLog->completed_at)
                <div>
                    <dt class="text-sm font-medium text-gray-400">Completed</dt>
                    <dd class="mt-1 text-sm text-white">{{ $exportLog->completed_at->format('M d, Y H:i') }}</dd>
                </div>
                @endif
            </dl>
        </div>
    </div>

    <!-- Actions -->
    <div class="flex justify-between">
        <a href="{{ route('campaigns.show', $exportLog->campaign) }}" 
           class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
            </svg>
            Back to Campaign
        </a>

        @if($exportLog->status === 'failed')
        <a href="{{ route('export.create', $exportLog->campaign) }}" 
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            Retry Export
        </a>
        @endif
    </div>
</div>
@endsection
