<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

$posts = get_posts(array(
    'post_type' => 'landing_page',
    'post_name' => 'pembuatan-website-ukm-startup',
    'post_status' => 'publish',
    'numberposts' => 1
));

if (!empty($posts)) {
    $post = $posts[0];
    echo 'Post ID: ' . $post->ID . PHP_EOL;
    echo 'Post Name: ' . $post->post_name . PHP_EOL;
    echo 'Post Title: ' . $post->post_title . PHP_EOL;
    
    // Check if class exists
    echo 'PageGenerator_Template_Handler class exists: ' . (class_exists('PageGenerator_Template_Handler') ? 'YES' : 'NO') . PHP_EOL;
    
    // Try to load the class manually
    $class_file = 'C:/xampp/htdocs/WP-Theme-dev/wp-content/plugins/wordpress-plugin/includes/class-template-handler.php';
    if (file_exists($class_file)) {
        echo 'Class file exists: YES' . PHP_EOL;
        require_once $class_file;
        echo 'Class loaded manually: ' . (class_exists('PageGenerator_Template_Handler') ? 'YES' : 'NO') . PHP_EOL;
        
        if (class_exists('PageGenerator_Template_Handler')) {
            $content = PageGenerator_Template_Handler::render_landing_page_content($post);
            echo 'Content length: ' . strlen($content) . PHP_EOL;
            echo 'Content preview (first 200 chars): ' . substr($content, 0, 200) . PHP_EOL;
        }
    } else {
        echo 'Class file exists: NO' . PHP_EOL;
    }
    
    // Check main content directly
    $main_content = get_post_meta($post->ID, 'page_generator_main_content', true);
    echo 'Main content length: ' . strlen($main_content) . PHP_EOL;
    echo 'Main content preview (first 200 chars): ' . substr($main_content, 0, 200) . PHP_EOL;
} else {
    echo 'Post not found' . PHP_EOL;
}
