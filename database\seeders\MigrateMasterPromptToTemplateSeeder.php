<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Template;
use App\Models\Campaign;

class MigrateMasterPromptToTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first campaign's master prompt (if exists)
        $campaign = Campaign::first();

        if ($campaign && $campaign->master_prompt) {
            // Update all templates with the master prompt from campaign
            Template::query()->update([
                'master_prompt' => $campaign->master_prompt
            ]);

            $this->command->info('Master prompt migrated from campaign to all templates.');
        } else {
            // Set a default master prompt for all templates
            $defaultPrompt = "You are an expert content creator for landing pages. Generate compelling, conversion-focused content for a professional business landing page.\n\nBusiness Context:\n- Business Name: {business_name}\n- Main Keyword: {main_keyword}\n- Target Audience: Small to medium businesses looking for professional services\n- Goal: Generate high-converting landing page content\n\nPlease create content that includes all the required fields for the template placeholders. Make the content engaging, professional, and optimized for conversions.";

            Template::query()->update([
                'master_prompt' => $defaultPrompt
            ]);

            $this->command->info('Default master prompt set for all templates.');
        }
    }
}
