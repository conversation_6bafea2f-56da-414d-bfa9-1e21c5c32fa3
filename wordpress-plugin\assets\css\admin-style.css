/* Page Generator Integration - Admin Styles */

.page-generator-wrap,
.pg-admin-container {
    max-width: 1200px;
}

.pg-header {
    margin-bottom: 20px;
}

.pg-header h1 {
    font-size: 24px;
    margin: 0 0 8px 0;
    color: #1d2327;
}

.pg-header p {
    color: #646970;
    margin: 0;
}

.page-generator-card,
.pg-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    padding: 24px;
    margin: 20px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
    transition: box-shadow 0.2s ease;
}

.page-generator-card:hover,
.pg-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,.1);
}

.page-generator-card h2,
.pg-card-header h2 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #1d2327;
    font-size: 20px;
    font-weight: 600;
}

.pg-card-header {
    border-bottom: 1px solid #e1e5e9;
    margin-bottom: 20px;
    padding-bottom: 12px;
}

.pg-card-body {
    padding: 0;
}

.page-generator-card h3 {
    color: #1d2327;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
}

.page-generator-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #00a32a;
    animation: pulse 2s infinite;
}

/* Form Styles */
.pg-form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.pg-form-col {
    flex: 1;
}

.pg-form-group {
    margin-bottom: 20px;
}

.pg-form-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
}

.pg-form-input:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.pg-code-editor {
    margin-bottom: 20px;
}

.pg-code-editor label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #1d2327;
}

.pg-code-editor textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #8c8f94;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
    resize: vertical;
}

.pg-code-editor textarea:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

/* Button Styles */
.pg-btn {
    display: inline-block;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pg-btn-primary {
    background: #2271b1;
    color: #fff;
}

.pg-btn-primary:hover {
    background: #135e96;
    color: #fff;
}

.pg-btn-secondary {
    background: #f6f7f7;
    color: #2c3338;
    border: 1px solid #8c8f94;
}

.pg-btn-secondary:hover {
    background: #f0f0f1;
    color: #2c3338;
}

.pg-btn-danger {
    background: #d63638;
    color: #fff;
}

.pg-btn-danger:hover {
    background: #b32d2e;
    color: #fff;
}

.pg-btn-group {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.pg-mt-1 {
    margin-top: 8px;
}

/* Table Styles */
.pg-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;
}

.pg-table th,
.pg-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e1e5e9;
}

.pg-table th {
    background: #f6f7f7;
    font-weight: 600;
    color: #1d2327;
}

.pg-table tr:hover {
    background: #f9f9f9;
}

.pg-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.pg-badge-success {
    background: #00a32a;
    color: #fff;
}

.pg-badge-warning {
    background: #dba617;
    color: #fff;
}

/* Utility Classes */
.pg-hidden {
    display: none;
}

.pg-shortcode-display {
    margin-top: 20px;
    padding: 16px;
    background: #f0f6fc;
    border: 1px solid #c3d9ff;
    border-radius: 4px;
}

.pg-shortcode-display span {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #fff;
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid #c3d9ff;
    cursor: pointer;
}

.status-indicator.offline {
    background: #d63638;
    animation: none;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 16px 0;
}

.feature-list li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-list li::before {
    content: "✅";
    font-size: 14px;
}

.api-endpoint {
    background: #f6f7f7;
    border: 1px solid #dcdcde;
    border-radius: 4px;
    padding: 12px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    word-break: break-all;
    margin: 12px 0;
}

.sections-table-wrapper {
    overflow-x: auto;
    margin: 16px 0;
}

.sections-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
}

.sections-table th,
.sections-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dcdcde;
}

.sections-table th {
    background: #f6f7f7;
    font-weight: 600;
    color: #1d2327;
}

.sections-table tr:hover {
    background: #f9f9f9;
}

.shortcode-display {
    background: #f6f7f7;
    border: 1px solid #dcdcde;
    border-radius: 4px;
    padding: 4px 8px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #d63638;
}

.section-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.section-actions a {
    text-decoration: none;
    color: #2271b1;
    font-size: 13px;
}

.section-actions a:hover {
    color: #135e96;
}

.section-actions .delete-link {
    color: #d63638;
}

.section-actions .delete-link:hover {
    color: #b32d2e;
}

.activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 300px;
    overflow-y: auto;
}

.activity-list li {
    padding: 12px;
    border-bottom: 1px solid #f0f0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-list li:last-child {
    border-bottom: none;
}

.activity-message {
    flex: 1;
    color: #1d2327;
}

.activity-time {
    color: #646970;
    font-size: 12px;
    font-style: italic;
}

.form-section {
    background: #f9f9f9;
    border: 1px solid #dcdcde;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.form-section h3 {
    margin-top: 0;
    color: #1d2327;
}

.code-editor {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    border: 1px solid #dcdcde;
    border-radius: 4px;
    padding: 12px;
    background: #f9f9f9;
    resize: vertical;
    min-height: 200px;
}

.code-editor:focus {
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
    outline: none;
}

.help-text {
    color: #646970;
    font-size: 13px;
    margin-top: 4px;
    font-style: italic;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin: 20px 0;
}

.stat-card {
    background: #fff;
    border: 1px solid #dcdcde;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #2271b1;
    display: block;
}

.stat-label {
    color: #646970;
    font-size: 13px;
    margin-top: 4px;
}

.notice-custom {
    border-left: 4px solid #00a32a;
    background: #f0f6fc;
    padding: 12px;
    margin: 16px 0;
    border-radius: 0 4px 4px 0;
}

.notice-custom.notice-warning {
    border-left-color: #dba617;
    background: #fcf9e8;
}

.notice-custom.notice-error {
    border-left-color: #d63638;
    background: #fcf0f1;
}

.button-group {
    display: flex;
    gap: 8px;
    margin: 16px 0;
}

.button-secondary {
    background: #f6f7f7;
    border: 1px solid #dcdcde;
    color: #2c3338;
}

.button-secondary:hover {
    background: #f0f0f1;
    border-color: #8c8f94;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-generator-card {
        padding: 16px;
        margin: 16px 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .section-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .api-endpoint {
        font-size: 11px;
        padding: 8px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .page-generator-card {
        background: #1d2327;
        border-color: #3c434a;
        color: #f0f0f1;
    }
    
    .page-generator-card h2,
    .page-generator-card h3 {
        color: #f0f0f1;
    }
    
    .api-endpoint {
        background: #2c3338;
        border-color: #3c434a;
        color: #f0f0f1;
    }
    
    .sections-table th {
        background: #2c3338;
        color: #f0f0f1;
    }
    
    .sections-table tr:hover {
        background: #2c3338;
    }
}

/* Custom Sections Styles */
.pg-section-editor {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.pg-code-editor {
    position: relative;
    margin: 10px 0;
}

.pg-code-editor label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #1d2327;
}

.pg-code-editor textarea {
    width: 100%;
    min-height: 200px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    background: #2d3748;
    color: #e2e8f0;
    border: 1px solid #4a5568;
    border-radius: 6px;
    padding: 15px;
    resize: vertical;
}

.pg-shortcode-display {
    background: #f1f5f9;
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    padding: 15px;
    font-family: monospace;
    font-size: 16px;
    color: #2563eb;
    text-align: center;
    margin: 15px 0;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pg-shortcode-display:hover {
    background: #e2e8f0;
    border-color: #2563eb;
}

.pg-form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.pg-form-col {
    flex: 1;
}

.pg-form-col label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #1d2327;
}

.pg-form-col input,
.pg-form-col textarea,
.pg-form-col select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccd0d4;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.pg-form-col input:focus,
.pg-form-col textarea:focus,
.pg-form-col select:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.pg-btn-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.pg-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.pg-btn-primary {
    background: #2563eb;
    color: white;
}

.pg-btn-primary:hover {
    background: #1d4ed8;
    color: white;
}

.pg-btn-secondary {
    background: #6b7280;
    color: white;
}

.pg-btn-secondary:hover {
    background: #4b5563;
    color: white;
}

.pg-btn-danger {
    background: #ef4444;
    color: white;
}

.pg-btn-danger:hover {
    background: #dc2626;
    color: white;
}
