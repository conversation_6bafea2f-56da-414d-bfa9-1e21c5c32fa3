@extends('layouts.app')

@section('title', 'Edit Template - ' . $template->name)
@section('page-title', 'Edit Template - ' . $template->name)

@section('content')
<div class="min-h-screen bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white">Edit Template</h1>
                    <p class="text-gray-400 mt-1">{{ $template->name }}</p>
                </div>
                <div class="flex items-center space-x-3">
                    @if($template->is_processed)
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-800 text-green-100">
                            Processed
                        </span>
                    @else
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-800 text-yellow-100">
                            Draft
                        </span>
                    @endif
                    
                    <a href="{{ route('templates.show', $template) }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>

        @if($errors->any())
            <div class="bg-red-800 border border-red-700 text-red-100 px-4 py-3 rounded mb-6">
                <ul class="list-disc list-inside">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form action="{{ route('templates.update', $template) }}" method="POST" id="template-form">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-lg font-semibold text-white mb-4">Informasi Dasar</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Nama Template</label>
                        <input type="text" name="name" id="name" value="{{ old('name', $template->name) }}" 
                               class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent" 
                               required>
                    </div>

                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-300 mb-2">Urutan</label>
                        <input type="number" name="sort_order" id="sort_order" value="{{ old('sort_order', $template->sort_order) }}" min="0"
                               class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent">
                    </div>
                </div>

                <div class="mt-6">
                    <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Deskripsi</label>
                    <textarea name="description" id="description" rows="3" 
                              class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                              placeholder="Deskripsi template (opsional)">{{ old('description', $template->description) }}</textarea>
                </div>

                <div class="mt-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_active" value="1" {{ old('is_active', $template->is_active) ? 'checked' : '' }}
                               class="rounded border-gray-600 text-cyan-600 shadow-sm focus:border-cyan-300 focus:ring focus:ring-cyan-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-300">Template aktif</span>
                    </label>
                </div>
            </div>

            <!-- Sections -->
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <div class="mb-4">
                    <h2 class="text-lg font-semibold text-white">Sections</h2>
                </div>

                <div id="sections-container">
                    <!-- Existing sections will be loaded here -->
                </div>

                <!-- Tambah Section Button - Moved to bottom -->
                <div class="mt-4 pt-4 border-t border-gray-700">
                    <button type="button" id="add-section"
                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Tambah Section
                    </button>
                </div>
            </div>

            <!-- Styles -->
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-lg font-semibold text-white mb-4">Custom CSS</h2>
                <textarea name="styles" id="styles" rows="10" 
                          class="w-full px-3 py-2 bg-gray-900 border border-gray-600 rounded-md text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                          placeholder="/* Custom CSS */
body {
    font-family: 'Inter', sans-serif;
}

.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}">{{ old('styles', $template->styles) }}</textarea>
            </div>

            <!-- Scripts -->
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-lg font-semibold text-white mb-4">Scripts & Links</h2>
                <textarea name="scripts" id="scripts" rows="8" 
                          class="w-full px-3 py-2 bg-gray-900 border border-gray-600 rounded-md text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                          placeholder="<!-- CSS Frameworks, JS Libraries -->
<script src='https://cdn.tailwindcss.com'></script>
<script src='https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js' defer></script>">{{ old('scripts', $template->scripts) }}</textarea>
            </div>

            <!-- Fonts -->
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-lg font-semibold text-white mb-4">Google Fonts</h2>
                <textarea name="fonts" id="fonts" rows="4" 
                          class="w-full px-3 py-2 bg-gray-900 border border-gray-600 rounded-md text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                          placeholder="<!-- Google Fonts -->
<link href='https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap' rel='stylesheet'>">{{ old('fonts', $template->fonts) }}</textarea>
            </div>

            <!-- Master Prompt -->
            <div class="bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-lg font-semibold text-white mb-4">Master Prompt</h2>
                @if($template->is_processed)
                    <div class="bg-blue-800 border border-blue-700 rounded p-3 mb-4">
                        <p class="text-sm text-blue-100">Template sudah diproses. Master prompt di-generate otomatis dari placeholders.</p>
                    </div>
                @else
                    <div class="bg-yellow-800 border border-yellow-700 rounded p-3 mb-4">
                        <p class="text-sm text-yellow-100">Master prompt akan di-generate otomatis setelah template diproses.</p>
                    </div>
                @endif
                <textarea name="master_prompt" id="master_prompt" rows="8" 
                          class="w-full px-3 py-2 bg-gray-900 border border-gray-600 rounded-md text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                          placeholder="Master prompt akan di-generate otomatis...">{{ old('master_prompt', $template->master_prompt) }}</textarea>
            </div>

            <!-- Submit -->
            <div class="flex items-center justify-end space-x-3">
                <a href="{{ route('templates.show', $template) }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md text-sm font-medium text-gray-300 bg-gray-700 hover:bg-gray-600">
                    Batal
                </a>
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Template
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let sectionIndex = 0;
    const sectionsContainer = document.getElementById('sections-container');
    const addSectionBtn = document.getElementById('add-section');

    // Load existing sections
    const existingSections = @json(old('sections', $template->sections ?? []));

    if (existingSections && existingSections.length > 0) {
        existingSections.forEach((section, index) => {
            addSection(section, index);
        });
    } else {
        // Add initial empty section if no existing sections
        addSection();
    }

    addSectionBtn.addEventListener('click', () => addSection());

    function addSection(sectionData = null, index = null) {
        const currentIndex = index !== null ? index : sectionIndex;
        const section = sectionData || { name: '', html_content: '', order: currentIndex };

        const sectionHtml = `
            <div class="section-item border border-gray-600 rounded-lg p-4 mb-4" data-index="${currentIndex}">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-md font-medium text-white">Section ${currentIndex + 1}</h3>
                    <div class="flex items-center space-x-2">
                        <button type="button" class="move-up text-gray-400 hover:text-white">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                            </svg>
                        </button>
                        <button type="button" class="move-down text-gray-400 hover:text-white">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <button type="button" class="remove-section text-red-400 hover:text-red-300">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Nama Section</label>
                        <input type="text" name="sections[${currentIndex}][name]" value="${section.name || ''}"
                               class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                               placeholder="Contoh: Hero Section" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Urutan</label>
                        <input type="number" name="sections[${currentIndex}][order]" value="${section.order || currentIndex}" min="0"
                               class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                               required>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">HTML Content</label>
                    <textarea name="sections[${currentIndex}][html_content]" rows="8"
                              class="w-full px-3 py-2 bg-gray-900 border border-gray-600 rounded-md text-white font-mono text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                              placeholder="<section class='hero-section'>
    <div class='container mx-auto px-4 py-16'>
        <h1 class='text-4xl font-bold text-center'>Judul Hero</h1>
        <p class='text-lg text-center mt-4'>Deskripsi hero section</p>
    </div>
</section>" required>${section.html_content || ''}</textarea>
                </div>
            </div>
        `;

        sectionsContainer.insertAdjacentHTML('beforeend', sectionHtml);

        // Add event listeners for the new section
        const newSection = sectionsContainer.lastElementChild;
        addSectionEventListeners(newSection);

        if (index === null) {
            sectionIndex++;
        } else {
            sectionIndex = Math.max(sectionIndex, index + 1);
        }

        updateSectionNumbers();
    }

    function addSectionEventListeners(section) {
        const removeBtn = section.querySelector('.remove-section');
        const moveUpBtn = section.querySelector('.move-up');
        const moveDownBtn = section.querySelector('.move-down');

        removeBtn.addEventListener('click', function() {
            if (sectionsContainer.children.length > 1) {
                section.remove();
                updateSectionNumbers();
            } else {
                alert('Template harus memiliki minimal 1 section');
            }
        });

        moveUpBtn.addEventListener('click', function() {
            const prevSection = section.previousElementSibling;
            if (prevSection) {
                sectionsContainer.insertBefore(section, prevSection);
                updateSectionNumbers();
            }
        });

        moveDownBtn.addEventListener('click', function() {
            const nextSection = section.nextElementSibling;
            if (nextSection) {
                sectionsContainer.insertBefore(nextSection, section);
                updateSectionNumbers();
            }
        });
    }

    function updateSectionNumbers() {
        const sections = sectionsContainer.querySelectorAll('.section-item');
        sections.forEach((section, index) => {
            const title = section.querySelector('h3');
            title.textContent = `Section ${index + 1}`;

            const orderInput = section.querySelector('input[name*="[order]"]');
            orderInput.value = index;

            // Update input names
            const inputs = section.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                const name = input.getAttribute('name');
                if (name) {
                    const newName = name.replace(/sections\[\d+\]/, `sections[${index}]`);
                    input.setAttribute('name', newName);
                }
            });
        });
    }
});
</script>
@endsection
