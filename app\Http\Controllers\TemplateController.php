<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Template;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;

class TemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        $templates = Template::orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('templates.index', compact('templates'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('templates.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sections' => 'required|array|min:1',
            'sections.*.name' => 'required|string|max:255',
            'sections.*.html_content' => 'required|string',
            'sections.*.order' => 'required|integer|min:0',
            'styles' => 'nullable|string',
            'scripts' => 'nullable|string',
            'fonts' => 'nullable|string',
            'master_prompt' => 'nullable|string',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        $template = Template::create($validated);

        return redirect()->route('templates.show', $template)
            ->with('success', 'Template berhasil dibuat!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Template $template): View
    {
        $template->load('generatedPages');

        return view('templates.show', compact('template'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Template $template): View
    {
        return view('templates.edit', compact('template'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Template $template): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'sections' => 'required|array|min:1',
            'sections.*.name' => 'required|string|max:255',
            'sections.*.html_content' => 'required|string',
            'sections.*.order' => 'required|integer|min:0',
            'styles' => 'nullable|string',
            'scripts' => 'nullable|string',
            'fonts' => 'nullable|string',
            'master_prompt' => 'nullable|string',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        $template->update($validated);

        return redirect()->route('templates.show', $template)
            ->with('success', 'Template berhasil diperbarui!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Template $template): RedirectResponse
    {
        // Check if template is being used
        if ($template->generatedPages()->exists()) {
            return back()->withErrors(['template' => 'Template tidak dapat dihapus karena masih digunakan.']);
        }

        $template->delete();

        return redirect()->route('templates.index')
            ->with('success', 'Template berhasil dihapus!');
    }

    /**
     * Preview template with sample data.
     */
    public function preview(Template $template): View
    {
        // Generate sample data for preview
        $sampleData = [
            'Yoast_title' => 'Preview Template - ' . $template->name,
            'Yoast_description' => 'Ini adalah preview template untuk ' . $template->name,
        ];

        // If template is processed, add placeholder sample data
        if ($template->is_processed && $template->placeholders) {
            foreach ($template->placeholders as $placeholder => $originalContent) {
                $sampleData[$placeholder] = '[SAMPLE] ' . $originalContent;
            }
        }

        try {
            $previewHtml = $template->replacePlaceholders($sampleData);
        } catch (\Exception $e) {
            $previewHtml = '<div style="padding: 20px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px; margin: 20px;">
                <h3>Preview Error</h3>
                <p>Error generating preview: ' . $e->getMessage() . '</p>
                <p>Template ID: ' . $template->id . '</p>
            </div>' . $template->generateCompleteHtml();
        }

        return view('templates.preview', compact('template', 'previewHtml'));
    }

    /**
     * Process template to generate placeholders.
     */
    public function process(Template $template): JsonResponse
    {
        try {
            // Generate placeholders from sections
            $placeholders = $template->processPlaceholders();

            // Generate master prompt
            $masterPrompt = $template->generateMasterPrompt();

            // Update template
            $template->update([
                'placeholders' => $placeholders,
                'master_prompt' => $masterPrompt,
                'is_processed' => true,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Template berhasil diproses!',
                'placeholders_count' => count($placeholders),
                'placeholders' => $placeholders,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error processing template: ' . $e->getMessage(),
            ], 500);
        }
    }
}
