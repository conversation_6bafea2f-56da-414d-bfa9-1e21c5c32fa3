<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

// Test clean architecture export
$page_data = array(
    'title' => 'Test Clean Architecture',
    'slug' => 'test-clean-architecture',
    'html_content' => '<h1>🚀 Test Clean Architecture</h1>
<p>Ini adalah konten HTML yang bisa diedit user di WordPress editor.</p>
<h2>✨ Fitur Baru</h2>
<ul>
<li>Konten masuk ke post_content</li>
<li>Assets terpisah di meta field</li>
<li>User bisa edit langsung</li>
</ul>
<div class="test-clean">Styled content dengan CSS dari assets</div>',
    'assets_content' => '<style>
.test-clean { 
    color: blue; 
    background: #f0f0f0; 
    padding: 20px; 
    border-radius: 8px;
    margin: 20px 0;
} 
.highlight { 
    background: yellow; 
}
</style>
<script>
console.log("Clean architecture assets loaded");
document.addEventListener("DOMContentLoaded", function() {
    console.log("DOM loaded with clean architecture");
});
</script>',
    'seo_title' => 'Test Clean Architecture - WebDev Pro',
    'seo_description' => 'Testing clean architecture implementation',
    'seo_keyword' => 'test clean architecture'
);

// Simulate AJAX call
$_POST['action'] = 'receive_laravel_clean_data';
$_POST['page_data'] = json_encode($page_data);

// Load plugin
if (file_exists(WP_PLUGIN_DIR . '/wordpress-plugin/page-generator-integration.php')) {
    include_once WP_PLUGIN_DIR . '/wordpress-plugin/page-generator-integration.php';

    // Get plugin instance
    $plugin = new PageGeneratorIntegration();

    // Call the create method directly (skip AJAX validation)
    $page_id = $plugin->create_clean_wordpress_page($page_data);
    $result = ($page_id > 0);
    
    if ($result) {
        echo "SUCCESS: Clean architecture export completed!" . PHP_EOL;
        echo "Created post ID: " . $page_id . PHP_EOL;

        // Get the created post
        $post = get_post($page_id);
        if ($post) {
            echo "Post content length: " . strlen($post->post_content) . PHP_EOL;
            echo "Post content preview: " . substr($post->post_content, 0, 200) . "..." . PHP_EOL;

            // Check meta fields
            $assets = get_post_meta($post->ID, '_page_generator_head_content', true);
            echo "Assets content length: " . strlen($assets) . PHP_EOL;
            echo "Assets preview: " . substr($assets, 0, 200) . "..." . PHP_EOL;
        }
    } else {
        echo "FAILED: Export failed, page_id: " . $page_id . PHP_EOL;
    }
} else {
    echo "ERROR: Plugin file not found" . PHP_EOL;
}
