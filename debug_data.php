<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

$posts = get_posts(array(
    'post_type' => 'landing_page',
    'post_status' => 'publish',
    'numberposts' => 1,
    'orderby' => 'date',
    'order' => 'DESC'
));

if (!empty($posts)) {
    $post = $posts[0];
    echo 'Post ID: ' . $post->ID . PHP_EOL;
    echo 'Post Name: ' . $post->post_name . PHP_EOL;
    echo 'Post Title: ' . $post->post_title . PHP_EOL;
    
    $page_data = get_post_meta($post->ID, 'page_data', true);
    $seo_data = get_post_meta($post->ID, 'seo_data', true);
    
    echo 'Page Data: ' . (empty($page_data) ? 'EMPTY' : 'EXISTS') . PHP_EOL;
    echo 'SEO Data: ' . (empty($seo_data) ? 'EMPTY' : 'EXISTS') . PHP_EOL;
    
    if (!empty($page_data)) {
        echo 'Has head_content: ' . (isset($page_data['head_content']) ? 'YES' : 'NO') . PHP_EOL;
        echo 'Has main_content: ' . (isset($page_data['main_content']) ? 'YES' : 'NO') . PHP_EOL;
    }
    
    // Check old format
    $old_head = get_post_meta($post->ID, 'page_generator_head_content', true);
    $old_main = get_post_meta($post->ID, 'page_generator_main_content', true);
    echo 'Old head content: ' . (empty($old_head) ? 'EMPTY' : 'EXISTS') . PHP_EOL;
    echo 'Old main content: ' . (empty($old_main) ? 'EMPTY' : 'EXISTS') . PHP_EOL;
    
    // Check Yoast data
    $yoast_title = get_post_meta($post->ID, '_yoast_wpseo_title', true);
    $yoast_desc = get_post_meta($post->ID, '_yoast_wpseo_metadesc', true);
    $yoast_kw = get_post_meta($post->ID, '_yoast_wpseo_focuskw', true);
    
    echo 'Yoast Title: ' . (empty($yoast_title) ? 'EMPTY' : $yoast_title) . PHP_EOL;
    echo 'Yoast Description: ' . (empty($yoast_desc) ? 'EMPTY' : $yoast_desc) . PHP_EOL;
    echo 'Yoast Keyword: ' . (empty($yoast_kw) ? 'EMPTY' : $yoast_kw) . PHP_EOL;
} else {
    echo 'No landing pages found' . PHP_EOL;
}
