<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('generated_pages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campaign_id')->constrained()->onDelete('cascade');
            $table->foreignId('template_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->string('slug')->unique();
            $table->longText('raw_html'); // Generated HTML content
            $table->longText('html_with_shortcodes'); // HTML with WordPress shortcodes
            $table->json('page_data'); // Original JSON data for this page
            $table->json('seo_data'); // Yoast SEO data (title, description, keywords, schema)
            $table->json('shortcode_mapping')->nullable(); // Mapping of shortcodes to values
            $table->string('wordpress_post_id')->nullable(); // WordPress post/page ID after export
            $table->enum('status', ['generated', 'exported', 'failed'])->default('generated');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('generated_pages');
    }
};
