<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExportLog extends Model
{
    protected $fillable = [
        'campaign_id',
        'wordpress_site_url',
        'total_pages',
        'exported_pages',
        'failed_pages',
        'export_settings',
        'error_message',
        'status',
        'started_at',
        'completed_at',
    ];

    protected $casts = [
        'export_settings' => 'array',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the campaign that owns the export log.
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Calculate export progress percentage.
     */
    public function getProgressPercentage(): float
    {
        if ($this->total_pages === 0) {
            return 0;
        }

        return round(($this->exported_pages / $this->total_pages) * 100, 2);
    }

    /**
     * Check if export is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if export has failed.
     */
    public function hasFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if export is in progress.
     */
    public function isInProgress(): bool
    {
        return $this->status === 'processing';
    }

    /**
     * Mark export as started.
     */
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'processing',
            'started_at' => now(),
        ]);
    }

    /**
     * Mark export as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    /**
     * Mark export as failed.
     */
    public function markAsFailed(string $errorMessage = null): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'completed_at' => now(),
        ]);
    }

    /**
     * Increment exported pages count.
     */
    public function incrementExported(): void
    {
        $this->increment('exported_pages');
    }

    /**
     * Increment failed pages count.
     */
    public function incrementFailed(): void
    {
        $this->increment('failed_pages');
    }
}
