Aplikasi ini adalah **Landingpage Generator** berbasis Laravel yang terhubung dengan WordPress melalui plugin **LaraPageGenerator**. Tuju<PERSON>ya adalah menghasilkan ribuan landing page unik secara otomatis, dengan dukungan **konten AI generator** agar lebih SEO friendly.

---

## Alur Kerja Aplikasi

1. **User membuat campaign baru**
    - Campaign berfungsi sebagai wadah untuk sekumpulan landing page sejenis.
2. **Pilih template landing page (LP)**
    - Template berisi struktur HTML dengan placeholder (misalnya `Hero_headline`, `Sub_headline`, `FAQ_section`, dll).
3. **Masukkan detail campaign**
    - User input keyword utama.
    - Detail lain seperti: nama bisnis, kontak, alama<PERSON>, deskripsi singkat, dll (untuk SEO).
    - Bagian *Keyword* juga bisa berisi kumpulan "people also ask" yang akan otomatis menjadi **FAQ section** di landing page.
4. **Master-Prompt untuk AI**
    - <PERSON><PERSON> men<PERSON> **konten generator** berupa master-prompt template.
    - Data yang dimasukkan user (keyword, kontak, alamat, dsb) akan otomatis ditanamkan ke dalam master-prompt.
    - Prompt ini ditampilkan secara real-time → user tinggal copy dan masukkan ke AI (misalnya ChatGPT atau LLM lain).
    - Hasil dari AI berupa file JSON yang berisi **banyak data sekaligus** (ribuan kombinasi konten unik untuk landing page).
5. **Upload file JSON ke aplikasi**
    - File JSON berisi key-value untuk mengganti placeholder pada template.
    - JSON juga berisi key khusus untuk Yoast SEO (title, description, focus keyword, schema, dll).
    - Contoh struktur JSON:
        
        ```json
        [
          {
            "Hero_headline": "Rental Mobil Bali Murah",
            "Sub_headline": "Layanan sewa mobil terbaik di Bali",
            "FAQ": [
              {"q": "Berapa harga rental mobil di Bali?", "a": "Mulai dari Rp 200.000 per hari."},
              {"q": "Apakah bisa sewa dengan sopir?", "a": "Ya, tersedia paket dengan sopir berpengalaman."}
            ],
            "Yoast_title": "Rental Mobil Bali | Harga Murah",
            "Yoast_description": "Sewa mobil Bali harga murah, layanan terbaik, banyak pilihan kendaraan.",
            "Yoast_focus_keyword": "rental mobil Bali"
          },
          {
            "Hero_headline": "Rental Mobil Jakarta 24 Jam",
            "Sub_headline": "Sewa mobil cepat dan mudah di Jakarta",
            "FAQ": [
              {"q": "Apakah tersedia sewa lepas kunci?", "a": "Ya, tersedia layanan lepas kunci dengan syarat mudah."}
            ],
            "Yoast_title": "Rental Mobil Jakarta | 24 Jam",
            "Yoast_description": "Sewa mobil Jakarta 24 jam nonstop, banyak pilihan armada terbaru.",
            "Yoast_focus_keyword": "rental mobil Jakarta"
          }
        ]
        
        ```
        
6. **Klik Generate**
    - Laravel memproses template + data JSON.
    - Output berupa:
        - **Raw HTML** (konten unik sesuai input JSON).
        - **Schema Markup SEO** (misalnya FAQ schema, business info schema).
        - **Shortcode WordPress** pada bagian tertentu (contoh: `[price]`).
7. **Export ke WordPress**
    - Laravel mengirim dua jenis data:
        1. **konten HTML dengan shortcode** → dikirim sebagai page/post ke WordPress.
        2. **Data campaign ke plugin LaraPageGenerator** → menyimpan mapping shortcode dengan konten aslinya.
        3. style css, js, font dan lain sebagainya yang ada di dalam template agar bisa di render oleh wordpress
    - Plugin LaraPageGenerator memungkinkan update massal. Misalnya jika `[price]` diubah, semua halaman otomatis ikut berubah.

---

## Contoh Template LP

```html
<!DOCTYPE html>
<html>
<head>
  <title>{{Yoast_title}}</title>
  <meta name="description" content="{{Yoast_description}}">
</head>
<body>
  <header>
    <h1>{{Hero_headline}}</h1>
    <p>{{Sub_headline}}</p>
  </header>

  <section id="pricing">
    [price]
  </section>

  <section id="faq">
    {{FAQ_section}}
  </section>

  <footer>
    <p>Kontak: {{Contact}}</p>
    <p>Alamat: {{Address}}</p>
  </footer>
</body>
</html>

```

---

## Manfaat

- Bisa menghasilkan **ribuan landing page otomatis** hanya dari 1 campaign + 1 file JSON.
- Konten lebih **SEO friendly** (termasuk FAQ + schema markup).
- Update massal lebih mudah via shortcode.
- Workflow fleksibel karena AI membantu generate JSON dalam jumlah besar.

---

## Panduan UI/UX

### Pendekatan Desain

- **Tema Global**: Dark minimalis (warna dominan: abu-abu gelap, aksen biru/turquoise untuk interaktif, putih untuk teks utama).
- **Konsistensi**: Semua komponen UI mengikuti **global style theme** agar seragam.
- **Mobile First**: Layout dirancang pertama kali untuk layar kecil (smartphone), kemudian diskalakan ke tablet dan desktop.
- **Tipografi**: Gunakan font sans-serif modern (misalnya Inter atau Roboto) dengan kontras tinggi untuk keterbacaan.

### Global Style Theme (AI Generated)

- **Background utama**: `#1E1E1E`
- **Card/Container**: `#2A2A2A` dengan rounded `lg` dan shadow halus.
- **Primary accent**: `#00ADB5`
- **Secondary accent**: `#393E46`
- **Text utama**: `#EEEEEE`
- **Text sekunder**: `#B8B8B8`
- **Button Primary**: Background `#00ADB5`, text putih, hover `#019CA5`
- **Input field**: Background `#2E2E2E`, border tipis `#393E46`, text `#EEEEEE`

### Struktur Layout Admin

- **Sidebar kiri (Admin Menu)**
    - Lebar tetap di desktop (±250px), collapsible di tablet/HP.
    - Icon + label (gunakan library seperti `lucide-react` atau `heroicons`).
    - Menu utama:
        1. Dashboard
        2. Campaigns
        3. Templates
        4. Content Generator (AI)
        5. Export Manager (WordPress)
        6. Settings
- **Header (opsional di mobile)**
    - Tampilkan toggle menu sidebar.
    - Quick actions (misal tombol “+ Campaign”).
- **Konten Utama**
    - Menggunakan grid responsive.
    - Card untuk setiap modul agar tampak konsisten.