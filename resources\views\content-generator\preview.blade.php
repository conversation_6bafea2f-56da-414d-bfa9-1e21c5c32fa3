@extends('layouts.app')

@section('title', 'Preview - ' . $page->title)
@section('page-title', 'Page Preview')

@section('content')
<div class="space-y-6">
    <!-- Page Info -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h3 class="text-lg font-medium text-white">{{ $page->page_data['Hero_headline'] ?? $page->title }}</h3>
                    <p class="text-sm text-gray-400">
                        Campaign: {{ $page->campaign->name }} • Template: {{ $page->template->name }}
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                        @if($page->status === 'generated') bg-green-800 text-green-100
                        @elseif($page->status === 'exported') bg-blue-800 text-blue-100
                        @elseif($page->status === 'draft') bg-yellow-800 text-yellow-100
                        @else bg-gray-700 text-gray-300 @endif">
                        {{ ucfirst($page->status) }}
                    </span>
                </div>
            </div>
            
            <!-- Page Details -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <dt class="text-sm font-medium text-gray-400">Slug</dt>
                    <dd class="mt-1 text-sm text-white">{{ $page->slug }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-400">Created</dt>
                    <dd class="mt-1 text-sm text-white">{{ $page->created_at->format('M d, Y H:i') }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-400">Updated</dt>
                    <dd class="mt-1 text-sm text-white">{{ $page->updated_at->format('M d, Y H:i') }}</dd>
                </div>
            </div>
        </div>
    </div>

    <!-- SEO Data -->
    @if($page->seo_data)
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">SEO Information</h3>
                <div class="space-y-3">
                    @if(isset($page->seo_data['title']))
                        <div>
                            <dt class="text-sm font-medium text-gray-400">Title</dt>
                            <dd class="mt-1 text-sm text-white">{{ $page->seo_data['title'] }}</dd>
                        </div>
                    @endif
                    @if(isset($page->seo_data['description']))
                        <div>
                            <dt class="text-sm font-medium text-gray-400">Description</dt>
                            <dd class="mt-1 text-sm text-white">{{ $page->seo_data['description'] }}</dd>
                        </div>
                    @endif
                    @if(isset($page->seo_data['focus_keyword']))
                        <div>
                            <dt class="text-sm font-medium text-gray-400">Focus Keyword</dt>
                            <dd class="mt-1 text-sm text-white">{{ $page->seo_data['focus_keyword'] }}</dd>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Actions -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-white mb-4">Actions</h3>
            <div class="flex flex-wrap gap-3">
                <button onclick="openPreviewWindow()" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    Preview in New Window
                </button>
                
                <button onclick="showRawHtml()" 
                        class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                    View HTML
                </button>
                
                <button onclick="showPageData()" 
                        class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    View Data
                </button>
                
                @if($page->status === 'generated')
                    <a href="{{ route('export.create', $page->campaign) }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        Export to WordPress
                    </a>
                @endif
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="flex justify-between">
        <a href="{{ route('campaigns.show', $page->campaign) }}" 
           class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Campaign
        </a>
        
        <a href="{{ route('content-generator.index') }}" 
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
            Generate More Pages
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
        </a>
    </div>
</div>

<!-- HTML Modal -->
<div id="htmlModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden z-50" onclick="closeModal('htmlModal')">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="inline-block align-bottom bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full sm:p-6" onclick="event.stopPropagation()">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg leading-6 font-medium text-white">Raw HTML</h3>
                <button onclick="closeModal('htmlModal')" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="relative">
                <textarea id="htmlContent" readonly 
                          class="w-full h-96 bg-gray-700 border border-gray-600 rounded-md p-3 text-white text-xs font-mono resize-none focus:outline-none focus:ring-cyan-500 focus:border-cyan-500"></textarea>
                <button onclick="copyHtml()" 
                        class="absolute top-2 right-2 px-3 py-1 bg-cyan-600 hover:bg-cyan-700 text-white text-xs rounded-md">
                    Copy
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Data Modal -->
<div id="dataModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden z-50" onclick="closeModal('dataModal')">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="inline-block align-bottom bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6" onclick="event.stopPropagation()">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg leading-6 font-medium text-white">Page Data</h3>
                <button onclick="closeModal('dataModal')" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="relative">
                <pre id="dataContent" class="w-full h-96 bg-gray-700 border border-gray-600 rounded-md p-3 text-white text-xs overflow-auto"></pre>
                <button onclick="copyData()" 
                        class="absolute top-2 right-2 px-3 py-1 bg-cyan-600 hover:bg-cyan-700 text-white text-xs rounded-md">
                    Copy
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function openPreviewWindow() {
    const url = '{{ route("content-generator.preview-window", $page) }}';
    window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
}

function showRawHtml() {
    // Use the raw HTML with shortcodes directly from the page object
    const rawHtml = {!! json_encode($page->html_with_shortcodes ?? '') !!};
    document.getElementById('htmlContent').value = rawHtml;
    document.getElementById('htmlModal').classList.remove('hidden');
}

function showPageData() {
    const data = {!! json_encode($page->page_data, JSON_PRETTY_PRINT) !!};
    document.getElementById('dataContent').textContent = JSON.stringify(data, null, 2);
    document.getElementById('dataModal').classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

function copyHtml() {
    const htmlContent = document.getElementById('htmlContent');
    htmlContent.select();
    document.execCommand('copy');
    showCopyFeedback(event.target);
}

function copyData() {
    const dataContent = document.getElementById('dataContent');
    const range = document.createRange();
    range.selectNode(dataContent);
    window.getSelection().removeAllRanges();
    window.getSelection().addRange(range);
    document.execCommand('copy');
    window.getSelection().removeAllRanges();
    showCopyFeedback(event.target);
}

function showCopyFeedback(button) {
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    button.classList.add('bg-green-600');
    button.classList.remove('bg-cyan-600');
    
    setTimeout(() => {
        button.textContent = originalText;
        button.classList.remove('bg-green-600');
        button.classList.add('bg-cyan-600');
    }, 2000);
}

// Close modals on Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeModal('htmlModal');
        closeModal('dataModal');
    }
});
</script>
@endpush
@endsection
