<?php
/**
 * Custom HTML Sections Manager
 * Handles creation, editing, and management of custom HTML sections with shortcodes
 */

if (!defined('ABSPATH')) {
    exit;
}

class PageGenerator_Custom_Sections {
    
    private $table_name;
    
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'custom_sections';
        
        add_action('init', array($this, 'init'));
        add_action('wp_ajax_save_custom_section', array($this, 'save_custom_section'));
        add_action('wp_ajax_delete_custom_section', array($this, 'delete_custom_section'));
        add_action('wp_ajax_get_custom_section', array($this, 'get_custom_section'));
        add_action('wp_ajax_generate_shortcode', array($this, 'generate_shortcode'));
    }
    
    public function init() {
        $this->create_sections_table();
        $this->register_dynamic_shortcodes();
    }
    
    /**
     * Create database table for custom sections
     */
    private function create_sections_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            shortcode varchar(100) NOT NULL UNIQUE,
            html_content longtext NOT NULL,
            css_content longtext,
            js_content longtext,
            description text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            status enum('active','inactive') DEFAULT 'active',
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Register all dynamic shortcodes from database
     */
    public function register_dynamic_shortcodes() {
        global $wpdb;
        
        $sections = $wpdb->get_results(
            "SELECT shortcode, html_content, css_content, js_content FROM {$this->table_name}"
        );
        
        foreach ($sections as $section) {
            add_shortcode($section->shortcode, function($atts, $content) use ($section) {
                return $this->render_custom_section($section, $atts, $content);
            });
        }
    }
    
    /**
     * Render custom section with HTML, CSS, and JS
     */
    private function render_custom_section($section, $atts = array(), $content = '') {
        $output = '';
        
        // Add CSS if exists
        if (!empty($section->css_content)) {
            $output .= '<style>' . $section->css_content . '</style>';
        }
        
        // Process HTML content with attributes
        $html = $section->html_content;
        
        // Replace placeholders with shortcode attributes
        if (!empty($atts)) {
            foreach ($atts as $key => $value) {
                $html = str_replace('{{' . $key . '}}', esc_html($value), $html);
            }
        }
        
        // Replace content placeholder
        if (!empty($content)) {
            $html = str_replace('{{content}}', $content, $html);
        }
        
        $output .= $html;
        
        // Add JavaScript if exists
        if (!empty($section->js_content)) {
            $output .= '<script>' . $section->js_content . '</script>';
        }
        
        return $output;
    }
    
    /**
     * Save custom section via AJAX
     */
    public function save_custom_section() {
        check_ajax_referer('page_generator_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $name = sanitize_text_field($_POST['name']);
        $shortcode = sanitize_text_field($_POST['shortcode']);
        $html_content = wp_kses_post($_POST['html_content']);
        $css_content = sanitize_textarea_field($_POST['css_content']);
        $js_content = sanitize_textarea_field($_POST['js_content']);
        $description = sanitize_textarea_field($_POST['description']);
        $section_id = intval($_POST['section_id']);
        
        global $wpdb;
        
        if ($section_id > 0) {
            // Update existing section
            $result = $wpdb->update(
                $this->table_name,
                array(
                    'name' => $name,
                    'shortcode' => $shortcode,
                    'html_content' => $html_content,
                    'css_content' => $css_content,
                    'js_content' => $js_content,
                    'description' => $description,
                    'updated_at' => current_time('mysql')
                ),
                array('id' => $section_id),
                array('%s', '%s', '%s', '%s', '%s', '%s', '%s'),
                array('%d')
            );
        } else {
            // Create new section
            $result = $wpdb->insert(
                $this->table_name,
                array(
                    'name' => $name,
                    'shortcode' => $shortcode,
                    'html_content' => $html_content,
                    'css_content' => $css_content,
                    'js_content' => $js_content,
                    'description' => $description
                ),
                array('%s', '%s', '%s', '%s', '%s', '%s')
            );
        }
        
        if ($result !== false) {
            wp_send_json_success(array(
                'message' => 'Section saved successfully',
                'section_id' => $section_id > 0 ? $section_id : $wpdb->insert_id
            ));
        } else {
            wp_send_json_error('Failed to save section');
        }
    }
    
    /**
     * Generate unique shortcode name
     */
    public function generate_shortcode() {
        check_ajax_referer('page_generator_nonce', 'nonce');
        
        $base_name = sanitize_text_field($_POST['base_name']);
        $shortcode = 'pg_' . sanitize_title($base_name);
        
        global $wpdb;
        
        // Check if shortcode exists
        $counter = 1;
        $original_shortcode = $shortcode;
        
        while ($wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->table_name} WHERE shortcode = %s",
            $shortcode
        )) > 0) {
            $shortcode = $original_shortcode . '_' . $counter;
            $counter++;
        }
        
        wp_send_json_success(array('shortcode' => $shortcode));
    }
    
    /**
     * Get all custom sections
     */
    public function get_all_sections() {
        global $wpdb;
        
        return $wpdb->get_results(
            "SELECT * FROM {$this->table_name} ORDER BY updated_at DESC"
        );
    }
    
    /**
     * Get single section by ID
     */
    public function get_section($id) {
        global $wpdb;
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE id = %d",
            $id
        ));
    }
    
    /**
     * Delete section via AJAX
     */
    public function delete_custom_section() {
        check_ajax_referer('page_generator_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $section_id = intval($_POST['section_id']);
        
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->table_name,
            array('id' => $section_id),
            array('%d')
        );
        
        if ($result !== false) {
            wp_send_json_success('Section deleted successfully');
        } else {
            wp_send_json_error('Failed to delete section');
        }
    }
    
    /**
     * Get section via AJAX
     */
    public function get_custom_section() {
        check_ajax_referer('page_generator_nonce', 'nonce');
        
        $section_id = intval($_POST['section_id']);
        $section = $this->get_section($section_id);
        
        if ($section) {
            wp_send_json_success($section);
        } else {
            wp_send_json_error('Section not found');
        }
    }
}

// Initialize the class
new PageGenerator_Custom_Sections();
