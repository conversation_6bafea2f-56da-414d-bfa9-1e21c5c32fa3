<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';
require_once 'C:/xampp/htdocs/WP-Theme-dev/wp-content/plugins/wordpress-plugin/includes/class-template-handler.php';

$posts = get_posts(array(
    'post_type' => 'landing_page',
    'post_name' => 'pembuatan-website-ukm-startup',
    'post_status' => 'publish',
    'numberposts' => 1
));

if (!empty($posts)) {
    $post = $posts[0];
    echo 'Post ID: ' . $post->ID . PHP_EOL;
    
    // Debug step by step
    $page_data = get_post_meta($post->ID, 'page_data', true);
    echo 'Page data empty: ' . (empty($page_data) ? 'YES' : 'NO') . PHP_EOL;
    
    if (!empty($page_data)) {
        echo 'Has main_content key: ' . (isset($page_data['main_content']) ? 'YES' : 'NO') . PHP_EOL;
        if (isset($page_data['main_content'])) {
            echo 'Main content from page_data length: ' . strlen($page_data['main_content']) . PHP_EOL;
        }
    }
    
    // Check old format
    $old_main = get_post_meta($post->ID, 'page_generator_main_content', true);
    echo 'Old main content length: ' . strlen($old_main) . PHP_EOL;
    
    // Test the method step by step
    $main_content = '';
    if (!empty($page_data) && isset($page_data['main_content'])) {
        $main_content = $page_data['main_content'];
        echo 'Using new format' . PHP_EOL;
    } else {
        $main_content = get_post_meta($post->ID, 'page_generator_main_content', true);
        echo 'Using old format' . PHP_EOL;
    }
    
    echo 'Selected main content length: ' . strlen($main_content) . PHP_EOL;
    echo 'Main content empty check: ' . (empty($main_content) ? 'YES' : 'NO') . PHP_EOL;
    
    if (!empty($main_content)) {
        // Test shortcode processing
        $processed = do_shortcode($main_content);
        echo 'After shortcode processing length: ' . strlen($processed) . PHP_EOL;
        
        // Test content filters
        $filtered = apply_filters('the_content', $processed);
        echo 'After content filters length: ' . strlen($filtered) . PHP_EOL;
    }
} else {
    echo 'Post not found' . PHP_EOL;
}
