<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TimezoneSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $timezones = [
            ['timezone' => 'UTC', 'label' => 'UTC (Coordinated Universal Time) - GMT+0'],
            ['timezone' => 'America/New_York', 'label' => 'Eastern Time (ET) - GMT-5'],
            ['timezone' => 'America/Chicago', 'label' => 'Central Time (CT) - GMT-6'],
            ['timezone' => 'America/Denver', 'label' => 'Mountain Time (MT) - GMT-7'],
            ['timezone' => 'America/Los_Angeles', 'label' => 'Pacific Time (PT) - GMT-8'],
            ['timezone' => 'Europe/London', 'label' => 'Greenwich Mean Time (GMT) - GMT+0'],
            ['timezone' => 'Europe/Paris', 'label' => 'Central European Time (CET) - GMT+1'],
            ['timezone' => 'Europe/Berlin', 'label' => 'Central European Time (CET) - GMT+1'],
            ['timezone' => 'Europe/Rome', 'label' => 'Central European Time (CET) - GMT+1'],
            ['timezone' => 'Europe/Madrid', 'label' => 'Central European Time (CET) - GMT+1'],
            ['timezone' => 'Europe/Amsterdam', 'label' => 'Central European Time (CET) - GMT+1'],
            ['timezone' => 'Asia/Tokyo', 'label' => 'Japan Standard Time (JST) - GMT+9'],
            ['timezone' => 'Asia/Shanghai', 'label' => 'China Standard Time (CST) - GMT+8'],
            ['timezone' => 'Asia/Hong_Kong', 'label' => 'Hong Kong Time (HKT) - GMT+8'],
            ['timezone' => 'Asia/Seoul', 'label' => 'Korea Standard Time (KST) - GMT+9'],
            ['timezone' => 'Asia/Kolkata', 'label' => 'India Standard Time (IST) - GMT+5:30'],
            ['timezone' => 'Asia/Dhaka', 'label' => 'Bangladesh Standard Time (BST) - GMT+6'],
            ['timezone' => 'Asia/Jakarta', 'label' => 'Western Indonesia Time (WIB) - GMT+7'],
            ['timezone' => 'Asia/Makassar', 'label' => 'Central Indonesia Time (WITA) - GMT+8'],
            ['timezone' => 'Asia/Jayapura', 'label' => 'Eastern Indonesia Time (WIT) - GMT+9'],
            ['timezone' => 'Asia/Bangkok', 'label' => 'Indochina Time (ICT) - GMT+7'],
            ['timezone' => 'Asia/Singapore', 'label' => 'Singapore Time (SGT) - GMT+8'],
            ['timezone' => 'Asia/Kuala_Lumpur', 'label' => 'Malaysia Time (MYT) - GMT+8'],
            ['timezone' => 'Asia/Manila', 'label' => 'Philippine Standard Time (PST) - GMT+8'],
            ['timezone' => 'Australia/Sydney', 'label' => 'Australian Eastern Time (AET) - GMT+10'],
            ['timezone' => 'Australia/Melbourne', 'label' => 'Australian Eastern Time (AET) - GMT+10'],
            ['timezone' => 'Australia/Perth', 'label' => 'Australian Western Time (AWT) - GMT+8'],
            ['timezone' => 'Pacific/Auckland', 'label' => 'New Zealand Standard Time (NZST) - GMT+12'],
            ['timezone' => 'Pacific/Fiji', 'label' => 'Fiji Time (FJT) - GMT+12'],
            ['timezone' => 'America/Sao_Paulo', 'label' => 'Brasília Time (BRT) - GMT-3'],
            ['timezone' => 'America/Mexico_City', 'label' => 'Central Standard Time (CST) - GMT-6'],
            ['timezone' => 'America/Toronto', 'label' => 'Eastern Time (ET) - GMT-5'],
            ['timezone' => 'America/Vancouver', 'label' => 'Pacific Time (PT) - GMT-8'],
            ['timezone' => 'Africa/Cairo', 'label' => 'Eastern European Time (EET) - GMT+2'],
            ['timezone' => 'Africa/Lagos', 'label' => 'West Africa Time (WAT) - GMT+1'],
            ['timezone' => 'Africa/Johannesburg', 'label' => 'South Africa Standard Time (SAST) - GMT+2'],
            ['timezone' => 'Europe/Moscow', 'label' => 'Moscow Time (MSK) - GMT+3'],
            ['timezone' => 'Europe/Istanbul', 'label' => 'Turkey Time (TRT) - GMT+3'],
            ['timezone' => 'Asia/Dubai', 'label' => 'Gulf Standard Time (GST) - GMT+4'],
            ['timezone' => 'Asia/Riyadh', 'label' => 'Arabia Standard Time (AST) - GMT+3'],
            ['timezone' => 'Asia/Tehran', 'label' => 'Iran Standard Time (IRST) - GMT+3:30'],
        ];

        foreach ($timezones as $timezone) {
            \DB::table('timezones')->insert([
                'timezone' => $timezone['timezone'],
                'label' => $timezone['label'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
