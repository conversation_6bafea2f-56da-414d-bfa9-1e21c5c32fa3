<?php

namespace App\Http\Controllers;

use App\Models\Timezone;
use App\Models\UserSettings;
use Illuminate\Http\Request;

class SettingsController extends Controller
{
    /**
     * Show the settings form.
     */
    public function index()
    {
        // Development mode: use session for user settings
        $userId = session('user_id', 1);
        $userSettings = UserSettings::where('user_id', $userId)->first() ?? new UserSettings(['timezone' => 'UTC']);

        // Get all available timezones from database
        $timezones = Timezone::orderBy('label')->pluck('label', 'timezone')->toArray();

        return view('settings.index', compact('userSettings', 'timezones'));
    }

    /**
     * Update the user's timezone settings.
     */
    public function update(Request $request)
    {
        $request->validate([
            'timezone' => 'required|string|timezone',
        ]);

        // Development mode: use session for user settings
        $userId = session('user_id', 1);

        // Create or update user settings
        UserSettings::updateOrCreate(
            ['user_id' => $userId],
            ['timezone' => $request->timezone]
        );

        return redirect()->route('settings.index')
            ->with('success', 'Pengaturan timezone berhasil disimpan.');
    }
}
