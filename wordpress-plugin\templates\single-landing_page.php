<?php
/**
 * Template for displaying single landing page posts
 * NEW CLEAN APPROACH: Simple template with header + content + footer
 *
 * Flow:
 * - Assets (CSS, JS, fonts) are injected via wp_head hook by inject_page_assets()
 * - HTML content is stored in post_content and fully editable by users
 * - SEO structure is maintained and optimized (no duplicates)
 */

get_header(); ?>

<main id="main" class="site-main">
    <?php
    while (have_posts()) :
        the_post();

        // NEW CLEAN ARCHITECTURE: Output raw content with basic formatting
        // User can edit this content directly in WordPress editor
        // Assets are handled separately via wp_head hook
        global $post;

        // Get raw content and apply only essential formatting
        $content = $post->post_content;
        $content = wpautop($content); // Add paragraphs
        $content = do_shortcode($content); // Process shortcodes
        echo $content;

    endwhile;
    ?>
</main>

<?php get_footer(); ?>