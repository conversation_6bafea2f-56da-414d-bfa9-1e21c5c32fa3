<?php
require 'C:/xampp/htdocs/WP-Theme-dev/wp-config.php';

$posts = get_posts(array(
    'post_type' => 'landing_page',
    'post_status' => 'publish',
    'numberposts' => 5
));

if (!empty($posts)) {
    echo "Found " . count($posts) . " landing pages:" . PHP_EOL;
    foreach($posts as $post) {
        echo 'ID: ' . $post->ID . ' | Slug: ' . $post->post_name . ' | Title: ' . $post->post_title . PHP_EOL;
        echo 'URL: ' . get_permalink($post->ID) . PHP_EOL;
        echo '---' . PHP_EOL;
    }
} else {
    echo "No landing pages found." . PHP_EOL;
}
