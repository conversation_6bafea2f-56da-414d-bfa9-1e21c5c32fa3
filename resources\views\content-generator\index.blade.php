@extends('layouts.app')

@section('title', 'Content Generator')
@section('page-title', 'AI Content Generator')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <h2 class="text-2xl font-bold text-white mb-2">AI Content Generator</h2>
        <p class="text-gray-400 max-w-3xl mx-auto">
            Generate thousands of unique landing pages using AI. Select a campaign, get the master prompt, 
            use it with your favorite AI tool, then upload the JSON result to generate pages automatically.
        </p>
    </div>

    <!-- How it Works -->
    <div class="bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-white mb-4">How It Works</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="mx-auto w-12 h-12 bg-cyan-500 rounded-lg flex items-center justify-center mb-3">
                        <span class="text-white font-bold">1</span>
                    </div>
                    <h4 class="text-sm font-medium text-white mb-2">Select Campaign</h4>
                    <p class="text-xs text-gray-400">Choose an existing campaign with your business details</p>
                </div>
                <div class="text-center">
                    <div class="mx-auto w-12 h-12 bg-cyan-500 rounded-lg flex items-center justify-center mb-3">
                        <span class="text-white font-bold">2</span>
                    </div>
                    <h4 class="text-sm font-medium text-white mb-2">Get Master Prompt</h4>
                    <p class="text-xs text-gray-400">Copy the generated prompt for your AI tool</p>
                </div>
                <div class="text-center">
                    <div class="mx-auto w-12 h-12 bg-cyan-500 rounded-lg flex items-center justify-center mb-3">
                        <span class="text-white font-bold">3</span>
                    </div>
                    <h4 class="text-sm font-medium text-white mb-2">Use AI Tool</h4>
                    <p class="text-xs text-gray-400">Paste prompt in ChatGPT, Claude, or other AI</p>
                </div>
                <div class="text-center">
                    <div class="mx-auto w-12 h-12 bg-cyan-500 rounded-lg flex items-center justify-center mb-3">
                        <span class="text-white font-bold">4</span>
                    </div>
                    <h4 class="text-sm font-medium text-white mb-2">Upload JSON</h4>
                    <p class="text-xs text-gray-400">Upload the AI-generated JSON to create pages</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign Selection -->
    @if($campaigns->count() > 0)
        <div class="bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-white mb-4">Select Campaign</h3>
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    @foreach($campaigns as $campaign)
                        <div class="bg-gray-700 rounded-lg p-4 hover:bg-gray-600 transition-colors duration-200">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-cyan-500 rounded-lg flex items-center justify-center mr-3">
                                        <span class="text-xs font-medium text-white">{{ substr($campaign->name, 0, 2) }}</span>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-white">{{ $campaign->name }}</h4>
                                        <p class="text-xs text-gray-400">{{ $campaign->business_name }}</p>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                    @if($campaign->status === 'active') bg-green-800 text-green-100
                                    @elseif($campaign->status === 'completed') bg-blue-800 text-blue-100
                                    @elseif($campaign->status === 'draft') bg-yellow-800 text-yellow-100
                                    @else bg-gray-700 text-gray-300 @endif">
                                    {{ ucfirst($campaign->status) }}
                                </span>
                            </div>
                            
                            <div class="space-y-2 mb-4">
                                <p class="text-xs text-gray-300">
                                    <span class="font-medium">Keyword:</span> {{ $campaign->main_keyword }}
                                </p>
                                @if($campaign->people_also_ask && count($campaign->people_also_ask) > 0)
                                    <p class="text-xs text-gray-300">
                                        <span class="font-medium">FAQ Questions:</span> {{ count($campaign->people_also_ask) }}
                                    </p>
                                @endif
                            </div>

                            <div class="flex space-x-2">
                                <button onclick="showMasterPrompt('{{ $campaign->id }}')" 
                                        class="flex-1 text-center px-3 py-2 border border-gray-600 text-xs font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                                    View Prompt
                                </button>
                                <a href="{{ route('content-generator.upload', $campaign) }}" 
                                   class="flex-1 text-center px-3 py-2 border border-transparent text-xs font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
                                    Upload JSON
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="mx-auto w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mb-4">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-white mb-2">No campaigns available</h3>
            <p class="text-gray-400 mb-6">Create a campaign first to start generating content.</p>
            <a href="{{ route('campaigns.create') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Campaign
            </a>
        </div>
    @endif

    <!-- Dummy JSON Download Section -->
    <div class="bg-blue-900/20 border border-blue-800 rounded-lg p-6 mt-8">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3 flex-1">
                <h3 class="text-lg font-medium text-blue-300 mb-2">Need Sample JSON Data for Testing?</h3>
                <p class="text-sm text-blue-200 mb-4">
                    Download our dummy JSON file containing 2 sample landing page data entries. Perfect for testing the content generation workflow before using your own AI-generated content.
                </p>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ asset('dummy-content.json') }}" download="dummy-content.json"
                       class="inline-flex items-center px-4 py-2 border border-blue-600 text-sm font-medium rounded-md text-blue-300 bg-blue-800/50 hover:bg-blue-700/50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download Dummy JSON
                    </a>
                    <button onclick="showJsonPreview()"
                            class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        Preview JSON
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JSON Preview Modal -->
<div id="jsonPreviewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-gray-800">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-white">Dummy JSON Preview</h3>
            <button onclick="closeJsonPreview()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="bg-gray-900 rounded-lg p-4 max-h-96 overflow-y-auto">
            <pre class="text-sm text-gray-300 whitespace-pre-wrap" id="jsonContent"></pre>
        </div>
        <div class="mt-4 flex justify-end space-x-2">
            <button onclick="copyJsonContent()"
                    class="px-4 py-2 bg-cyan-600 text-white rounded-md hover:bg-cyan-700">
                Copy JSON
            </button>
            <button onclick="closeJsonPreview()"
                    class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                Close
            </button>
        </div>
    </div>
</div>

<!-- Master Prompt Modal -->
<div id="promptModal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden z-50" onclick="closeModal()">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="inline-block align-bottom bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6" onclick="event.stopPropagation()">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg leading-6 font-medium text-white">Master Prompt</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="mb-4">
                <p class="text-sm text-gray-400 mb-3">Copy this prompt and paste it into your AI tool (ChatGPT, Claude, etc.):</p>
                <div class="relative">
                    <textarea id="promptText" readonly 
                              class="w-full h-64 bg-gray-700 border border-gray-600 rounded-md p-3 text-white text-sm font-mono resize-none focus:outline-none focus:ring-cyan-500 focus:border-cyan-500"></textarea>
                    <button onclick="copyPrompt()" 
                            class="absolute top-2 right-2 px-3 py-1 bg-cyan-600 hover:bg-cyan-700 text-white text-xs rounded-md">
                        Copy
                    </button>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button onclick="closeModal()" 
                        class="px-4 py-2 border border-gray-600 text-sm font-medium rounded-md text-gray-300 bg-gray-700 hover:bg-gray-600">
                    Close
                </button>
                <button onclick="regeneratePrompt()" 
                        class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-cyan-600 hover:bg-cyan-700">
                    Regenerate Prompt
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let currentCampaignId = null;

function showMasterPrompt(campaignId) {
    currentCampaignId = campaignId;
    
    fetch(`/content-generator/${campaignId}/master-prompt`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('promptText').value = data.prompt;
            document.getElementById('promptModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading master prompt');
        });
}

function closeModal() {
    document.getElementById('promptModal').classList.add('hidden');
    currentCampaignId = null;
}

function copyPrompt() {
    const promptText = document.getElementById('promptText');
    promptText.select();
    document.execCommand('copy');
    
    // Show feedback
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    button.classList.add('bg-green-600');
    button.classList.remove('bg-cyan-600');
    
    setTimeout(() => {
        button.textContent = originalText;
        button.classList.remove('bg-green-600');
        button.classList.add('bg-cyan-600');
    }, 2000);
}

function regeneratePrompt() {
    if (!currentCampaignId) return;
    
    fetch(`/content-generator/${currentCampaignId}/regenerate-prompt`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('promptText').value = data.prompt;
        alert(data.message);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error regenerating prompt');
    });
}

// JSON Preview functions
function showJsonPreview() {
    fetch('{{ asset("dummy-content.json") }}')
        .then(response => response.json())
        .then(data => {
            document.getElementById('jsonContent').textContent = JSON.stringify(data, null, 2);
            document.getElementById('jsonPreviewModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error loading JSON:', error);
            alert('Error loading JSON file');
        });
}

function closeJsonPreview() {
    document.getElementById('jsonPreviewModal').classList.add('hidden');
}

function copyJsonContent() {
    const jsonContent = document.getElementById('jsonContent').textContent;
    navigator.clipboard.writeText(jsonContent).then(() => {
        alert('JSON content copied to clipboard!');
    }).catch(err => {
        console.error('Error copying to clipboard:', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = jsonContent;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('JSON content copied to clipboard!');
    });
}

// Close modal on Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeModal();
        closeJsonPreview();
    }
});
</script>
@endpush
@endsection
